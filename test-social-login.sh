#!/bin/bash

echo "Testing /auth/social-login endpoint..."
echo ""

# Test 1: Missing provider
echo "Test 1: Missing provider"
curl -X POST http://localhost:8080/auth/social-login \
  -H "Content-Type: application/json" \
  -d '{"accessToken": "test-token"}' \
  -w "\nStatus: %{http_code}\n\n"

# Test 2: Missing token
echo "Test 2: Missing token"
curl -X POST http://localhost:8080/auth/social-login \
  -H "Content-Type: application/json" \
  -d '{"provider": "GOOGLE"}' \
  -w "\nStatus: %{http_code}\n\n"

# Test 3: Unsupported provider
echo "Test 3: Unsupported provider"
curl -X POST http://localhost:8080/auth/social-login \
  -H "Content-Type: application/json" \
  -d '{"provider": "FACEBOOK", "accessToken": "test-token"}' \
  -w "\nStatus: %{http_code}\n\n"

# Test 4: Invalid Google token
echo "Test 4: Invalid Google token"
curl -X POST http://localhost:8080/auth/social-login \
  -H "Content-Type: application/json" \
  -d '{"provider": "GOOGLE", "accessToken": "invalid-google-token"}' \
  -w "\nStatus: %{http_code}\n\n"

echo "Note: To test with a real Google token, replace 'invalid-google-token' with an actual Google ID token"
echo "You can get one from: https://developers.google.com/oauthplayground/"
