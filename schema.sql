--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Homebrew)
-- Dumped by pg_dump version 14.18 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: cube; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS cube WITH SCHEMA public;


--
-- Name: EXTENSION cube; Type: COMMENT; Schema: -; Owner:
--

COMMENT ON EXTENSION cube IS 'data type for multidimensional cubes';


--
-- Name: earthdistance; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS earthdistance WITH SCHEMA public;


--
-- Name: EXTENSION earthdistance; Type: COMMENT; Schema: -; Owner:
--

COMMENT ON EXTENSION earthdistance IS 'calculate great-circle distances on the surface of the Earth';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: auth_users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.auth_users (
    id character varying(255) NOT NULL,
    account_non_expired boolean NOT NULL,
    account_non_locked boolean NOT NULL,
    created_at timestamp without time zone,
    credentials_non_expired boolean NOT NULL,
    email character varying(255) NOT NULL,
    enabled boolean NOT NULL,
    last_login_at timestamp without time zone,
    password character varying(255) NOT NULL,
    phone character varying(255) NOT NULL,
    updated_at timestamp without time zone,
    username character varying(255) NOT NULL,
    user_id character varying(255)
);


ALTER TABLE public.auth_users OWNER TO postgres;

--
-- Name: group_time_slots; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.group_time_slots (
    group_id character varying(255) NOT NULL,
    time_slot character varying(255)
);


ALTER TABLE public.group_time_slots OWNER TO postgres;

--
-- Name: groups; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.groups (
    id character varying(255) NOT NULL,
    center_lat double precision NOT NULL,
    center_lon double precision NOT NULL,
    created_at timestamp without time zone,
    finalized_at timestamp without time zone,
    gender character varying(255),
    is_finalized boolean NOT NULL,
    updated_at timestamp without time zone
);


ALTER TABLE public.groups OWNER TO postgres;

--
-- Name: hibernate_sequence; Type: SEQUENCE; Schema: public; Owner: mytribe_user
--

CREATE SEQUENCE public.hibernate_sequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.hibernate_sequence OWNER TO mytribe_user;

--
-- Name: quiz_answers; Type: TABLE; Schema: public; Owner: mytribe_user
--

CREATE TABLE public.quiz_answers (
    id bigint NOT NULL,
    answer character varying(255) NOT NULL,
    question_id integer NOT NULL,
    submitted_at timestamp without time zone,
    user_id character varying(255) NOT NULL
);


ALTER TABLE public.quiz_answers OWNER TO mytribe_user;

--
-- Name: quiz_answers_id_seq; Type: SEQUENCE; Schema: public; Owner: mytribe_user
--

CREATE SEQUENCE public.quiz_answers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.quiz_answers_id_seq OWNER TO mytribe_user;

--
-- Name: quiz_answers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: mytribe_user
--

ALTER SEQUENCE public.quiz_answers_id_seq OWNED BY public.quiz_answers.id;


--
-- Name: user_interests; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_interests (
    user_id character varying(255) NOT NULL,
    interest character varying(255)
);


ALTER TABLE public.user_interests OWNER TO postgres;

--
-- Name: user_personality; Type: TABLE; Schema: public; Owner: mytribe_user
--

CREATE TABLE public.user_personality (
    user_id character varying(255) NOT NULL,
    personality character varying(32),
    scored_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.user_personality OWNER TO mytribe_user;

--
-- Name: user_quiz_answers; Type: TABLE; Schema: public; Owner: mytribe_user
--

CREATE TABLE public.user_quiz_answers (
    id integer NOT NULL,
    user_id character varying(255) NOT NULL,
    question_id integer NOT NULL,
    answer text NOT NULL,
    submitted_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.user_quiz_answers OWNER TO mytribe_user;

--
-- Name: user_quiz_answers_id_seq; Type: SEQUENCE; Schema: public; Owner: mytribe_user
--

CREATE SEQUENCE public.user_quiz_answers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.user_quiz_answers_id_seq OWNER TO mytribe_user;

--
-- Name: user_quiz_answers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: mytribe_user
--

ALTER SEQUENCE public.user_quiz_answers_id_seq OWNED BY public.user_quiz_answers.id;


--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_roles (
    user_id character varying(255) NOT NULL,
    role character varying(255)
);


ALTER TABLE public.user_roles OWNER TO postgres;

--
-- Name: user_time_slots; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_time_slots (
    user_id character varying(255) NOT NULL,
    time_slot character varying(255)
);


ALTER TABLE public.user_time_slots OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id character varying(255) NOT NULL,
    bio character varying(255),
    city character varying(255),
    created_at timestamp without time zone,
    employment_status character varying(255),
    ethnicity character varying(255),
    gender character varying(255),
    hobby character varying(255),
    lat double precision NOT NULL,
    location_enabled boolean NOT NULL,
    lon double precision NOT NULL,
    name character varying(255),
    profession character varying(255),
    relationship_status character varying(255),
    state character varying(255),
    updated_at timestamp without time zone,
    zip character varying(255),
    group_id character varying(255),
    fun_fact character varying(150),
    onboarding_step character varying(50) NOT NULL DEFAULT 'REGISTERED'
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: user_progress; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_progress (
    user_id character varying(255) NOT NULL,
    step character varying(50) NOT NULL,
    completed_at timestamp without time zone NOT NULL
);


ALTER TABLE public.user_progress OWNER TO postgres;

--
-- Name: quiz_answers id; Type: DEFAULT; Schema: public; Owner: mytribe_user
--

ALTER TABLE ONLY public.quiz_answers ALTER COLUMN id SET DEFAULT nextval('public.quiz_answers_id_seq'::regclass);


--
-- Name: user_quiz_answers id; Type: DEFAULT; Schema: public; Owner: mytribe_user
--

ALTER TABLE ONLY public.user_quiz_answers ALTER COLUMN id SET DEFAULT nextval('public.user_quiz_answers_id_seq'::regclass);


--
-- Name: auth_users auth_users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.auth_users
    ADD CONSTRAINT auth_users_pkey PRIMARY KEY (id);


--
-- Name: groups groups_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.groups
    ADD CONSTRAINT groups_pkey PRIMARY KEY (id);


--
-- Name: quiz_answers quiz_answers_pkey; Type: CONSTRAINT; Schema: public; Owner: mytribe_user
--

ALTER TABLE ONLY public.quiz_answers
    ADD CONSTRAINT quiz_answers_pkey PRIMARY KEY (id);


--
-- Name: auth_users uk_6jqfsuvys3lan090p4mk16a5t; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.auth_users
    ADD CONSTRAINT uk_6jqfsuvys3lan090p4mk16a5t UNIQUE (email);


--
-- Name: auth_users uk_f9wqm8ya8k2x456jqotu3ihla; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.auth_users
    ADD CONSTRAINT uk_f9wqm8ya8k2x456jqotu3ihla UNIQUE (username);


--
-- Name: auth_users uk_n07let3tu7foo4wj8tmhqb4hw; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.auth_users
    ADD CONSTRAINT uk_n07let3tu7foo4wj8tmhqb4hw UNIQUE (phone);


--
-- Name: user_personality user_personality_pkey; Type: CONSTRAINT; Schema: public; Owner: mytribe_user
--

ALTER TABLE ONLY public.user_personality
    ADD CONSTRAINT user_personality_pkey PRIMARY KEY (user_id);


--
-- Name: user_quiz_answers user_quiz_answers_pkey; Type: CONSTRAINT; Schema: public; Owner: mytribe_user
--

ALTER TABLE ONLY public.user_quiz_answers
    ADD CONSTRAINT user_quiz_answers_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: user_progress user_progress_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_progress
    ADD CONSTRAINT user_progress_pkey PRIMARY KEY (user_id, step);


--
-- Name: user_roles fk333amu2tr91i761renbrkwxgk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT fk333amu2tr91i761renbrkwxgk FOREIGN KEY (user_id) REFERENCES public.auth_users(id);


--
-- Name: user_interests fkdv9fflrh61wyuujfwx2yn1tb4; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_interests
    ADD CONSTRAINT fkdv9fflrh61wyuujfwx2yn1tb4 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: users fkemfuglprp85bh5xwhfm898ysc; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT fkemfuglprp85bh5xwhfm898ysc FOREIGN KEY (group_id) REFERENCES public.groups(id);


--
-- Name: user_progress fk_user_progress_user_id; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_progress
    ADD CONSTRAINT fk_user_progress_user_id FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: group_time_slots fkhd73cfi9tf7sq9j8y7vdbsnrq; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.group_time_slots
    ADD CONSTRAINT fkhd73cfi9tf7sq9j8y7vdbsnrq FOREIGN KEY (group_id) REFERENCES public.groups(id);


--
-- Name: user_time_slots fkp03sm6qssu4njnysk8wjasr3w; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_time_slots
    ADD CONSTRAINT fkp03sm6qssu4njnysk8wjasr3w FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: auth_users fkrpvvat76cqj7gfiyhoso7skyq; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.auth_users
    ADD CONSTRAINT fkrpvvat76cqj7gfiyhoso7skyq FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: TABLE auth_users; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE public.auth_users TO mytribe_user;


--
-- Name: TABLE group_time_slots; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE public.group_time_slots TO mytribe_user;


--
-- Name: TABLE groups; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE public.groups TO mytribe_user;


--
-- Name: TABLE user_interests; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE public.user_interests TO mytribe_user;


--
-- Name: TABLE user_roles; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE public.user_roles TO mytribe_user;


--
-- Name: TABLE user_time_slots; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE public.user_time_slots TO mytribe_user;


--
-- Name: TABLE users; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE public.users TO mytribe_user;


--
-- PostgreSQL database dump complete
--

