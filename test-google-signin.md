# Google Sign-In Test Guide

## Setup Complete ✅

The Google Sign-In integration has been implemented with the provided credentials:

- **Client ID**: `57098642797-q9vik2ts8o4g8trir0vdiem50o1j5acr.apps.googleusercontent.com`
- **Redirect URI**: `http://localhost:3000/auth/google/callback`

## How to Test

### 1. Start the Application

**Backend:**
```bash
./mvnw spring-boot:run
```

**Frontend:**
```bash
cd frontend
npm start
```

### 2. Test Google Sign-In

1. **Navigate to**: `http://localhost:3000/login`

2. **Click the Google Sign-In Button**: 
   - The blue Google icon in the social login section
   - Should trigger Google One Tap authentication

3. **Complete Google Authentication**:
   - Sign in with your Google account
   - Grant permissions when prompted

4. **Verify Success**:
   - Check browser console for "Google Sign-In successful" log
   - Should see success message with user info on the page
   - Backend should log successful token verification

### 3. Expected Flow

```
User clicks Google button → Google One Tap appears → 
User signs in → Frontend receives ID token → 
POST /auth/social-login → Backend verifies token → 
Returns user information → Success message displayed
```

### 4. API Request Format

The frontend sends:
```json
POST /auth/social-login
{
  "provider": "GOOGLE",
  "accessToken": "eyJhbGciOiJSUzI1NiIs..." // Google ID token
}
```

Backend responds:
```json
{
  "email": "<EMAIL>",
  "name": "User Name",
  "googleUserId": "*********",
  "profilePicture": "https://...",
  "emailVerified": true,
  "success": true,
  "message": "Google token verified successfully"
}
```

## Implementation Details

### ✅ Frontend Features
- Google Identity Services integration
- One Tap authentication flow
- Proper error handling and user feedback
- Loading states and visual feedback
- Fallback error messages for blocked popups

### ✅ Backend Features
- Google ID token verification using GoogleIdTokenVerifier
- Audience validation against provided Client ID
- User information extraction (email, name, Google user ID)
- Comprehensive error handling and logging
- Production-ready security validation

### ✅ Configuration
- Real Google Client ID configured in both frontend and backend
- Environment variable support for production deployment
- Proper CORS configuration for localhost:3000
- No hardcoded credentials or test data

## Troubleshooting

### Common Issues

1. **"Google One Tap is not available"**
   - Check if browser blocks popups or has ad blockers
   - Try refreshing the page
   - Check browser console for errors

2. **"Google Sign-In is not available"**
   - Verify Google script loaded: check `window.google` in console
   - Check network connectivity
   - Ensure Client ID is correct

3. **Token verification fails**
   - Verify Client ID matches in frontend and backend
   - Check that domain is authorized in Google Console
   - Ensure token hasn't expired

### Debug Steps

1. **Check Browser Console**: Look for initialization and error logs
2. **Check Network Tab**: Verify POST request to `/auth/social-login`
3. **Check Backend Logs**: Look for token verification logs
4. **Verify Configuration**: Ensure Client ID is correct in both places

## Next Steps

Once user database and JWT logic is implemented, the flow will:

1. ✅ Verify Google ID token (already working)
2. 🚧 Create or find user in database
3. 🚧 Generate JWT token for session
4. 🚧 Redirect to appropriate onboarding step
5. 🚧 Complete login flow integration

The Google Sign-In foundation is solid and ready for full authentication integration! 🎉
