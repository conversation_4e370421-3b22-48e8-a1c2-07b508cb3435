import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Container,
  Grid,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Card,
  CardContent,
  Slide,
  CircularProgress,
  useTheme,
  alpha,
  IconButton
} from '@mui/material';
import Carousel from 'react-material-ui-carousel';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import SecurityIcon from '@mui/icons-material/Security';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import appConfig from '../../config/appConfig';
import userService from '../../services/user.service';
import {
  pageBackgroundStyle,
  mainCardStyle,
  cardHeaderStyle,
  bottomNavStyle,
  homeButtonStyle,
  navButtonStyle,
  primaryButtonStyle,
  secondaryButtonStyle,
  outlinedButtonStyle,
  dialogPaperStyle,
  inputLabelProps,
  inputProps
} from '../../styles/appStyles';

// Transition component for dialog
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const PaymentLanding = ({ user }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [nearbyEvents, setNearbyEvents] = useState([]);
  const [userLocation, setUserLocation] = useState('');
  const [userZip, setUserZip] = useState('');
  const [locationDialogOpen, setLocationDialogOpen] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);

  // Payment form state
  const [paymentForm, setPaymentForm] = useState({
    cardNumber: '',
    cardName: '',
    expiry: '',
    cvv: ''
  });

  // Load events and user location on component mount
  useEffect(() => {
    // Get user data from localStorage
    const userData = JSON.parse(localStorage.getItem('user') || '{}');

    // Set user location from profile if available
    if (userData.profile) {
      if (userData.profile.zipCode) {
        setUserZip(userData.profile.zipCode);
      }

      // Set location display based on available profile data
      if (userData.profile.city && userData.profile.state) {
        setUserLocation(`${userData.profile.city}, ${userData.profile.state}`);
      } else if (userData.profile.city) {
        setUserLocation(userData.profile.city);
      } else if (userData.profile.state) {
        setUserLocation(userData.profile.state);
      } else if (userData.profile.zipCode) {
        setUserLocation(`Zip: ${userData.profile.zipCode}`);
      } else {
        setUserLocation('USA');
      }
    }

    // Generate nearby events based on user location
    const generateNearbyEvents = () => {
      // In a real app, these would come from an API based on the user's location
      const eventOptions = [
        {
          id: 1,
          name: 'Central Park Picnic',
          type: 'Outdoor Events',
          image: '/images/events/park.svg',
          distance: '1.2 miles away'
        },
        {
          id: 2,
          name: 'Beach Volleyball',
          type: 'Sports & Recreation',
          image: '/images/events/beach.svg',
          distance: '2.5 miles away'
        },
        {
          id: 3,
          name: 'Downtown Food Festival',
          type: 'Food & Dining',
          image: '/images/events/food.svg',
          distance: '0.8 miles away'
        }
      ];

      // For demo purposes, select events based on user data if available
      // In a real app, this would be based on user preferences and location
      if (userData.profile && userData.profile.interests) {
        const userInterests = userData.profile.interests.map(i => i.toLowerCase());

        // Filter events that might match user interests
        const matchingEvents = eventOptions.filter(event => {
          const eventType = event.type.toLowerCase();
          return userInterests.some(interest => eventType.includes(interest));
        });

        // Use matching events if found, otherwise use all options
        return matchingEvents.length > 0 ? matchingEvents : [eventOptions[0]];
      }

      // Default to first event if no user interests
      return [eventOptions[0]];
    };

    // Simulate API call to get nearby events
    setTimeout(() => {
      const events = generateNearbyEvents();
      setNearbyEvents(events);
      setLoading(false);
    }, 1500);
  }, []);

  // Handle payment form change
  const handlePaymentFormChange = (e) => {
    const { name, value } = e.target;
    setPaymentForm({
      ...paymentForm,
      [name]: value
    });
  };

  // Handle payment button click
  const handlePayment = () => {
    setPaymentDialogOpen(true);
  };

  // Handle process payment
  const handleProcessPayment = () => {
    setProcessingPayment(true);

    // Simulate payment processing
    setTimeout(() => {
      setProcessingPayment(false);
      setPaymentDialogOpen(false);
      setSuccessDialogOpen(true);
    }, 2000);
  };

  // Handle payment success
  const handlePaymentSuccess = async () => {
    try {
      // Complete onboarding by calling the backend
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      if (currentUser.userId) {
        await userService.completePayment(currentUser.userId);
        console.log('Onboarding completed successfully');
      }
    } catch (error) {
      console.error('Error completing onboarding:', error);
      // Continue anyway - don't block user flow
    }

    setSuccessDialogOpen(false);
    navigate('/payment-success');
  };

  // Handle location change
  const handleLocationChange = () => {
    setLocationDialogOpen(true);
  };

  // Handle location dialog close
  const handleLocationDialogClose = () => {
    setLocationDialogOpen(false);
  };

  // Handle location update
  const handleLocationUpdate = () => {
    // Validate zip code
    if (!userZip || userZip.trim() === '') {
      alert('Please enter a valid zip code');
      return;
    }

    // In a real app, this would convert zip to city/state via API
    // For now, just use the zip code
    setUserLocation(`Zip: ${userZip}`);

    // Update user profile in localStorage with new zip
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    if (userData.profile) {
      userData.profile.zipCode = userZip;
      localStorage.setItem('user', JSON.stringify(userData));
    }

    setLocationDialogOpen(false);
  };



  return (
    <Box sx={pageBackgroundStyle}>
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 2 }}>
        <Card sx={mainCardStyle}>
          <CardContent sx={{ p: 0 }}>
            {/* Header */}
            <Box sx={cardHeaderStyle}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Chip
                  icon={<LocationOnIcon />}
                  label={userLocation || 'USA'}
                  variant="outlined"
                  size="small"
                  sx={{
                    color: 'white',
                    borderColor: 'rgba(255,255,255,0.3)',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
                    Secure Your Spot for Just ${appConfig.membershipPrice}
                  </Typography>

                  <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.7)', mb: 1 }}>
                    Your payment is 100% safe & refundable!
                  </Typography>

                  <Button
                    variant="text"
                    color="warning"
                    onClick={handleLocationChange}
                    sx={{ textTransform: 'none', p: 0, fontWeight: 600 }}
                  >
                    Change location
                  </Button>
                </Box>

                <Box sx={{ position: 'relative', display: { xs: 'none', sm: 'block' } }}>
                  <Box
                    component="img"
                    src="/images/secure-payment.svg"
                    alt="Secure Payment"
                    sx={{
                      width: 120,
                      height: 'auto',
                      objectFit: 'contain'
                    }}
                  />
                </Box>
              </Box>
            </Box>

            {/* Event Carousel */}
            <Box sx={{ bgcolor: 'white', color: 'text.primary', p: 2 }}>
              <Carousel
                animation="slide"
                indicators={true}
                navButtonsAlwaysInvisible={false}
                autoPlay={true}
                interval={4000}
                sx={{
                  '& .MuiPaper-root': {
                    borderRadius: 3,
                    overflow: 'hidden'
                  }
                }}
              >
                {loading ? (
                  <Paper sx={{ height: 180, bgcolor: 'rgba(0,0,0,0.1)' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress />
                    </Box>
                  </Paper>
                ) : (
                  nearbyEvents.map((event) => (
                    <Paper key={event.id} sx={{ height: 180, position: 'relative' }}>
                      <Box
                        component="img"
                        src={event.image}
                        alt={event.name}
                        sx={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          p: 2,
                          background: 'linear-gradient(to top, rgba(0,0,0,0.8), transparent)',
                        }}
                      >
                        <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)' }}>
                          {event.type}
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600, color: 'white' }}>
                          {event.name}
                        </Typography>
                      </Box>
                    </Paper>
                  ))
                )}
              </Carousel>

              {/* Payment Button */}
              <Button
                fullWidth
                variant="contained"
                color="primary"
                size="large"
                onClick={handlePayment}
                sx={{
                  py: 2,
                  mt: 2,
                  borderRadius: 8,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  bgcolor: '#F5A623',
                  color: '#000',
                  '&:hover': {
                    bgcolor: '#e69c1f'
                  }
                }}
              >
                Reserve my spot now at ${appConfig.membershipPrice}
              </Button>
            </Box>

            {/* Info Cards */}
            <Box sx={{ p: 3 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  borderRadius: 3,
                  bgcolor: 'rgba(255,255,255,0.05)',
                  mb: 3,
                  border: '1px solid rgba(255,255,255,0.1)'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <Box
                    component="img"
                    src="/images/avatar-icon.svg"
                    alt="Avatar"
                    sx={{
                      width: 40,
                      height: 40,
                      mr: 2,
                      borderRadius: '50%',
                      objectFit: 'cover'
                    }}
                  />
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      Your ${appConfig.membershipPrice} simply saves your spot.
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      You'll only be charged when your group of {appConfig.maxGroupSize} is confirmed.
                    </Typography>
                  </Box>
                </Box>
              </Paper>

              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  borderRadius: 3,
                  bgcolor: 'rgba(255,255,255,0.05)',
                  mb: 3,
                  border: '1px solid rgba(255,255,255,0.1)'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <Box
                    component="img"
                    src="/images/refund-icon.svg"
                    alt="Refund"
                    sx={{
                      width: 40,
                      height: 40,
                      mr: 2,
                      borderRadius: '4px',
                      objectFit: 'cover'
                    }}
                  />
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      100% refundable if you change your mind
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      before your group of {appConfig.maxGroupSize} is formed.
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </CardContent>
        </Card>

        {/* Bottom Navigation */}
        <Paper
          elevation={3}
          sx={bottomNavStyle}
        >
          <Container maxWidth="sm">
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-around',
              py: 1
            }}>
              <Button
                color="inherit"
                sx={{
                  flexDirection: 'column',
                  color: 'rgba(255,255,255,0.7)',
                  minWidth: 'auto'
                }}
              >
                <NotificationsIcon />
                <Typography variant="caption">Notification</Typography>
              </Button>

              <Button
                color="primary"
                sx={{
                  flexDirection: 'column',
                  bgcolor: 'rgba(245, 166, 35, 0.2)',
                  borderRadius: '50%',
                  p: 1,
                  minWidth: 'auto',
                  '&:hover': {
                    bgcolor: 'rgba(245, 166, 35, 0.3)',
                  }
                }}
              >
                <HomeIcon />
                <Typography variant="caption">Home</Typography>
              </Button>

              <Button
                color="inherit"
                sx={{
                  flexDirection: 'column',
                  color: 'rgba(255,255,255,0.7)',
                  minWidth: 'auto'
                }}
              >
                <SettingsIcon />
                <Typography variant="caption">Settings</Typography>
              </Button>
            </Box>
          </Container>
        </Paper>
      </Container>

      {/* Location Dialog */}
      <Dialog
        open={locationDialogOpen}
        onClose={handleLocationDialogClose}
        TransitionComponent={Transition}
        maxWidth="xs"
        fullWidth
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{ color: 'white', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Update Your Location</DialogTitle>
        <DialogContent sx={{ color: 'white' }}>
          <TextField
            autoFocus
            margin="dense"
            id="zip"
            label="Zip Code"
            type="text"
            fullWidth
            variant="outlined"
            value={userZip}
            onChange={(e) => setUserZip(e.target.value)}
            InputLabelProps={inputLabelProps}
            InputProps={inputProps}
          />
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <Button onClick={handleLocationDialogClose} sx={outlinedButtonStyle}>Cancel</Button>
          <Button onClick={handleLocationUpdate} variant="contained" sx={primaryButtonStyle}>
            Update
          </Button>
        </DialogActions>
      </Dialog>

      {/* Payment Dialog */}
      <Dialog
        open={paymentDialogOpen}
        TransitionComponent={Transition}
        keepMounted
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', color: 'white', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CreditCardIcon sx={{ mr: 1, color: '#F5A623' }} />
            Payment Details
          </Box>
          <Button
            onClick={() => setPaymentDialogOpen(false)}
            disabled={processingPayment}
            sx={{ minWidth: 'auto', p: 1, color: 'white' }}
          >
            <CloseIcon />
          </Button>
        </DialogTitle>
        <DialogContent sx={{ color: 'white' }}>
          <Typography variant="subtitle1" sx={{ mb: 3 }}>
            You will be charged ${appConfig.membershipPrice} to secure your spot.
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Card Number"
                name="cardNumber"
                value={paymentForm.cardNumber}
                onChange={handlePaymentFormChange}
                fullWidth
                placeholder="1234 5678 9012 3456"
                disabled={processingPayment}
                InputLabelProps={inputLabelProps}
                InputProps={inputProps}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Cardholder Name"
                name="cardName"
                value={paymentForm.cardName}
                onChange={handlePaymentFormChange}
                fullWidth
                placeholder="John Doe"
                disabled={processingPayment}
                InputLabelProps={inputLabelProps}
                InputProps={inputProps}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Expiry Date"
                name="expiry"
                value={paymentForm.expiry}
                onChange={handlePaymentFormChange}
                fullWidth
                placeholder="MM/YY"
                disabled={processingPayment}
                InputLabelProps={inputLabelProps}
                InputProps={inputProps}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="CVV"
                name="cvv"
                value={paymentForm.cvv}
                onChange={handlePaymentFormChange}
                fullWidth
                placeholder="123"
                disabled={processingPayment}
                InputLabelProps={inputLabelProps}
                InputProps={inputProps}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3, borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <Button
            onClick={() => setPaymentDialogOpen(false)}
            disabled={processingPayment}
            sx={outlinedButtonStyle}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleProcessPayment}
            disabled={processingPayment}
            startIcon={processingPayment ? <CircularProgress size={20} color="inherit" /> : null}
            sx={primaryButtonStyle}
          >
            {processingPayment ? 'Processing...' : `Pay $${appConfig.membershipPrice}`}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success Dialog */}
      <Dialog
        open={successDialogOpen}
        TransitionComponent={Transition}
        maxWidth="xs"
        fullWidth
        PaperProps={{ sx: dialogPaperStyle }}
      >
        <DialogContent sx={{ textAlign: 'center', py: 4, color: 'white' }}>
          <CheckCircleIcon sx={{ fontSize: 60, color: '#F5A623', mb: 2 }} />
          <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
            Payment Successful!
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Your spot has been secured. You'll be notified when your group is formed.
          </Typography>
          <Button
            variant="contained"
            fullWidth
            onClick={handlePaymentSuccess}
            sx={primaryButtonStyle}
          >
            Continue
          </Button>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default PaymentLanding;
