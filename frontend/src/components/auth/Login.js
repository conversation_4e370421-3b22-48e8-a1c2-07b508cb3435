import React, { useState, useContext } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Button,
  TextField,
  Link,
  Grid,
  Box,
  Typography,
  Alert,
  useTheme,
  alpha,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  IconButton,
  InputAdornment,
  Card,
  CardContent
} from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import FacebookIcon from '@mui/icons-material/Facebook';
import AppleIcon from '@mui/icons-material/Apple';
import { ThemeContext } from '../../contexts/ThemeContext';
import AuthService from '../../services/auth.service';

const Login = ({ onLogin }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { mode } = useContext(ThemeContext);

  const [formData, setFormData] = useState({
    emailOrPhone: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Forgot password states
  const [forgotPasswordOpen, setForgotPasswordOpen] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetEmailSent, setResetEmailSent] = useState(false);
  const [resetEmailError, setResetEmailError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Toggle password visibility
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Handle back button click
  const handleBackClick = () => {
    navigate('/');
  };

  // Handle opening the forgot password dialog
  const handleForgotPasswordOpen = () => {
    setForgotPasswordOpen(true);
    // Pre-fill with the email from the login form if available
    if (formData.emailOrPhone && formData.emailOrPhone.includes('@')) {
      setResetEmail(formData.emailOrPhone);
    } else {
      setResetEmail('');
    }
    setResetEmailSent(false);
    setResetEmailError('');
  };

  // Handle closing the forgot password dialog
  const handleForgotPasswordClose = () => {
    setForgotPasswordOpen(false);
  };

  // Handle reset email change
  const handleResetEmailChange = (e) => {
    setResetEmail(e.target.value);
    setResetEmailError('');
  };

  // Handle sending password reset email
  const handleSendResetEmail = async () => {
    // Validate email
    if (!resetEmail || !resetEmail.includes('@')) {
      setResetEmailError('Please enter a valid email address');
      return;
    }

    try {
      // Call backend API to send password reset email
      await AuthService.requestPasswordReset(resetEmail);
      setResetEmailSent(true);

      // Close dialog after showing success message
      setTimeout(() => {
        setForgotPasswordOpen(false);
      }, 3000);
    } catch (error) {
      console.error('Password reset failed:', error);
      if (error.response && error.response.status === 404) {
        setResetEmailError('No account found with this email address');
      } else {
        setResetEmailError('Failed to send reset email. Please try again.');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Use AuthService to login
      const userData = await AuthService.login(formData.emailOrPhone, formData.password);

      // Handle successful login
      onLogin(userData);

      // Redirect based on user role or onboarding step
      if (userData.role === 'ADMIN') {
        navigate('/admin');
      } else if (userData.profileCompleted) {
        navigate('/group-status');
      } else if (userData.nextRoute) {
        // Use the onboarding-aware routing from backend
        navigate(userData.nextRoute);
      } else {
        // Fallback to user registration
        navigate('/user-registration');
      }

    } catch (err) {
      console.error('Login failed:', err);

      // Handle different types of errors
      if (err.response && err.response.status === 404) {
        // User not found - redirect to registration
        sessionStorage.setItem('registrationEmail', formData.emailOrPhone);
        navigate('/signup', {
          state: {
            message: 'Email not found. Please register a new account.',
            email: formData.emailOrPhone
          }
        });
      } else if (err.response && err.response.status === 401) {
        setError('Incorrect password. Please try again or reset your password.');
      } else {
        setError('Login failed. Please check your connection and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Background style with image overlay
  const backgroundStyle = {
    backgroundImage: 'url(/images/beach-fun.jpg)',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    position: 'relative',
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 1
    }
  };

  return (
    <Box sx={backgroundStyle}>
      <Card sx={{
        maxWidth: 500,
        width: '100%',
        borderRadius: 4,
        boxShadow: 3,
        position: 'relative',
        zIndex: 2,
        bgcolor: 'rgba(18, 18, 18, 0.8)',
        color: 'white',
        overflow: 'hidden'
      }}>
        <CardContent sx={{ p: 0 }}>
          {/* Header with back button */}
          <Box sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <IconButton onClick={handleBackClick} edge="start" sx={{ mr: 2, color: 'white' }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" component="h1" sx={{ fontWeight: 500, color: 'white' }}>
              Login
            </Typography>
          </Box>
          <Box
            sx={{
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              gap: 2
            }}
          >
            <Typography
              component="h1"
              variant="h4"
              sx={{
                fontWeight: 700,
                mb: 1,
                textAlign: 'center',
                color: 'white'
              }}
            >
              Log in
            </Typography>
            <Typography
              variant="body1"
              sx={{ mb: 2, textAlign: 'center', color: 'rgba(255, 255, 255, 0.7)' }}
            >
              Hi Welcome back, you've been missed
            </Typography>

            {error && (
              <Alert
                severity="error"
                sx={{
                  width: '100%',
                  mb: 2,
                  borderRadius: 2,
                  boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.2)}`
                }}
              >
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="emailOrPhone"
                label="Enter email or phone number"
                name="emailOrPhone"
                autoComplete="email"
                autoFocus
                value={formData.emailOrPhone}
                onChange={handleChange}
                variant="outlined"
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#F5A623',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 0.7)',
                    '&.Mui-focused': {
                      color: '#F5A623',
                    },
                  },
                  '& .MuiInputBase-input': {
                    color: 'white',
                  },
              }}
            />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="Enter password"
                type={showPassword ? 'text' : 'password'}
                id="password"
                autoComplete="current-password"
                value={formData.password}
                onChange={handleChange}
                variant="outlined"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                        sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                ),
              }}
              sx={{
                  mb: 1,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#F5A623',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 0.7)',
                    '&.Mui-focused': {
                      color: '#F5A623',
                    },
                  },
                  '& .MuiInputBase-input': {
                    color: 'white',
                  },
              }}
            />

              {/* Forgot Password Link */}
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Link
                  component="button"
                  variant="body2"
                  onClick={handleForgotPasswordOpen}
                  sx={{
                    color: '#F5A623',
                    textDecoration: 'none',
                    '&:hover': { textDecoration: 'underline' },
                    background: 'none',
                    border: 'none',
                    padding: 0,
                    fontWeight: 500
                  }}
                >
                  Forgot Password?
                </Link>
              </Box>

              {/* Login Button */}
              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{
                  mt: 2,
                  py: 1.5,
                  bgcolor: '#F5A623',
                  color: 'white',
                  borderRadius: 8,
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500,
                  '&:hover': {
                    bgcolor: '#d48c1f',
                  },
                }}
                disabled={loading}
              >
                {loading ? 'Logging in...' : 'Login'}
              </Button>

              {/* Or Divider */}
              <Box sx={{ display: 'flex', alignItems: 'center', my: 3 }}>
                <Box sx={{ flex: 1, height: 1, bgcolor: 'rgba(255,255,255,0.1)' }} />
                <Typography variant="body2" sx={{ px: 2, color: 'rgba(255,255,255,0.5)' }}>
                  Or
                </Typography>
                <Box sx={{ flex: 1, height: 1, bgcolor: 'rgba(255,255,255,0.1)' }} />
              </Box>

              {/* Social Login Buttons */}
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 3 }}>
                <IconButton
                  sx={{
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: 2,
                    p: 1.5,
                    color: 'white'
                  }}
                >
                  <FacebookIcon />
                </IconButton>
                <IconButton
                  sx={{
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: 2,
                    p: 1.5
                  }}
                >
                  <Box component="img" src="https://img.icons8.com/color/48/000000/google-logo.png" sx={{ width: 24, height: 24 }} />
                </IconButton>
                <IconButton
                  sx={{
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: 2,
                    p: 1.5,
                    color: 'white'
                  }}
                >
                  <AppleIcon />
                </IconButton>
              </Box>

              {/* Sign Up Link */}
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.7)' }} display="inline">
                  Don't have an account ?
                </Typography>
                <Link
                  component={RouterLink}
                  to="/signup"
                  variant="body2"
                  sx={{
                    color: '#F5A623',
                    textDecoration: 'none',
                    ml: 1,
                    fontWeight: 500,
                    '&:hover': { textDecoration: 'underline' }
                  }}
                >
                  Sign Up
                </Link>
              </Box>

              {/* Admin credentials info */}
              <Box sx={{ mt: 3, p: 2, bgcolor: 'rgba(255, 255, 255, 0.05)', borderRadius: 2 }}>
                <Typography variant="subtitle2" gutterBottom sx={{ color: 'white' }}>
                  Admin Credentials:
                </Typography>
                <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Email: <EMAIL><br />
                  Password: admin123
                </Typography>
              </Box>
          </Box>
        </Box>
        {/* Forgot Password Dialog */}
        <Dialog open={forgotPasswordOpen} onClose={handleForgotPasswordClose}>
          <DialogTitle>Reset Password</DialogTitle>
          <DialogContent>
            <DialogContentText sx={{ mb: 2 }}>
              {resetEmailSent ?
                'Password reset email sent! Check your inbox for instructions.' :
                'Enter your email address and we\'ll send you a link to reset your password.'}
            </DialogContentText>
            {!resetEmailSent && (
              <TextField
                autoFocus
                margin="dense"
                id="reset-email"
                label="Email Address"
                type="email"
                fullWidth
                variant="outlined"
                value={resetEmail}
                onChange={handleResetEmailChange}
                error={!!resetEmailError}
                helperText={resetEmailError}
                disabled={resetEmailSent}
              />
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleForgotPasswordClose} color="primary">
              {resetEmailSent ? 'Close' : 'Cancel'}
            </Button>
            {!resetEmailSent && (
              <Button
                onClick={handleSendResetEmail}
                color="primary"
                variant="contained"
              >
                Send Reset Link
              </Button>
            )}
          </DialogActions>
        </Dialog>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Login;
