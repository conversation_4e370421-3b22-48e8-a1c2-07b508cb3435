import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box, Typography, CircularProgress, Alert } from '@mui/material';
import AuthService from '../../services/auth.service';

const GoogleCallback = ({ onLogin }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const handleGoogleCallback = async () => {
      try {
        // Extract authorization code from URL parameters
        const urlParams = new URLSearchParams(location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');

        if (error) {
          console.error('Google OAuth error:', error);
          setError('Google Sign-In was cancelled or failed. Please try again.');
          setLoading(false);
          // Redirect back to login after 3 seconds
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        if (!code) {
          console.error('No authorization code received from Google');
          setError('Invalid Google Sign-In response. Please try again.');
          setLoading(false);
          // Redirect back to login after 3 seconds
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        console.log('Received Google authorization code:', code);

        // For now, we'll show a message that the redirect flow needs backend implementation
        // In a production setup, the backend should handle the code exchange
        setError('Google redirect flow requires backend implementation. Please use the One Tap flow instead.');
        setLoading(false);
        // Redirect back to login after 3 seconds
        setTimeout(() => navigate('/login'), 3000);
        return;

        // TODO: Implement backend endpoint to handle authorization code exchange
        // The backend should:
        // 1. Receive the authorization code
        // 2. Exchange it for tokens using client_secret
        // 3. Verify the ID token
        // 4. Return user information or JWT

        // Example of how it would work:
        // const result = await AuthService.googleCodeLogin(code);
        // if (result.success) {
        //   console.log('Google Sign-In successful:', result);
        //   if (onLogin) onLogin(result);
        //   // Redirect based on user data...
        // }

      } catch (err) {
        console.error('Google callback error:', err);
        setError('Google Sign-In failed. Please try again.');
        setLoading(false);
        // Redirect back to login after 3 seconds
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleGoogleCallback();
  }, [location, navigate, onLogin]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#000',
        backgroundImage: 'url(/images/beach-fun.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          zIndex: 1
        }
      }}
    >
      <Box
        sx={{
          position: 'relative',
          zIndex: 2,
          textAlign: 'center',
          color: 'white',
          p: 4
        }}
      >
        {loading ? (
          <>
            <CircularProgress sx={{ color: 'white', mb: 2 }} size={60} />
            <Typography variant="h5" sx={{ mb: 1 }}>
              Completing Google Sign-In...
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.8 }}>
              Please wait while we verify your Google account
            </Typography>
          </>
        ) : (
          <>
            <Alert
              severity="error"
              sx={{
                mb: 2,
                backgroundColor: 'rgba(211, 47, 47, 0.1)',
                color: 'white',
                '& .MuiAlert-icon': {
                  color: '#f44336'
                }
              }}
            >
              {error}
            </Alert>
            <Typography variant="body1" sx={{ opacity: 0.8 }}>
              Redirecting to login page...
            </Typography>
          </>
        )}
      </Box>
    </Box>
  );
};

export default GoogleCallback;
