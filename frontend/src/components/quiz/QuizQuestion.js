import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  IconButton,
  Paper,
  Radio,
  FormControlLabel,
  RadioGroup,
  FormControl,
  CircularProgress,
  useTheme,
  Card,
  CardContent,
  Alert,
  Snackbar
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import quizService from '../../services/quiz.service';
import authService from '../../services/auth.service';

// Quiz questions data
const quizQuestions = [
  {
    id: 1,
    question: "If you were given an extra hour each day, how would you choose to spend it?",
    options: [
      { id: 'A', text: "Plan a fun activity." },
      { id: 'B', text: "Catch up via call or text." },
      { id: 'C', text: "Hang out casually." },
      { id: 'D', text: "Take time for myself, then check in." }
    ]
  },
  {
    id: 2,
    question: "When faced with a challenging situation in a group, what's your typical approach?",
    options: [
      { id: 'A', text: "Take charge and organize a solution." },
      { id: 'B', text: "Listen to everyone's ideas before deciding." },
      { id: 'C', text: "Offer emotional support to those who need it." },
      { id: 'D', text: "Analyze the problem and suggest logical solutions." }
    ]
  },
  {
    id: 3,
    question: "What aspect of your career matters most to you right now?",
    options: [
      { id: 'A', text: "Work-life balance and flexibility." },
      { id: 'B', text: "Making a meaningful impact." },
      { id: 'C', text: "Learning new skills and growing." },
      { id: 'D', text: "Financial stability and advancement." }
    ]
  },
  {
    id: 4,
    question: "How do you recharge after a socially demanding week?",
    options: [
      { id: 'A', text: "Solo activities like reading or hiking." },
      { id: 'B', text: "Small gathering with close friends." },
      { id: 'C', text: "Creative pursuits or hobbies." },
      { id: 'D', text: "Physical exercise or outdoor activities." }
    ]
  },
  {
    id: 5,
    question: "What's your approach to making major life decisions?",
    options: [
      { id: 'A', text: "Research thoroughly and weigh all options." },
      { id: 'B', text: "Follow my intuition and gut feeling." },
      { id: 'C', text: "Seek advice from trusted friends and family." },
      { id: 'D', text: "Consider how it aligns with my long-term goals." }
    ]
  }
];

const QuizQuestion = () => {
  const navigate = useNavigate();
  const { questionId } = useParams();
  const theme = useTheme();

  const currentQuestionId = parseInt(questionId, 10);
  const currentQuestion = quizQuestions.find(q => q.id === currentQuestionId);
  const currentUser = authService.getCurrentUser();

  const [selectedOption, setSelectedOption] = useState('');
  const [answers, setAnswers] = useState(() => {
    // Load saved answers from localStorage if available
    const savedAnswers = localStorage.getItem('quizAnswers');
    return savedAnswers ? JSON.parse(savedAnswers) : {};
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showError, setShowError] = useState(false);

  // Set initial selected option if answer exists
  useEffect(() => {
    if (answers[currentQuestionId]) {
      setSelectedOption(answers[currentQuestionId]);
    } else {
      setSelectedOption('');
    }
  }, [currentQuestionId, answers]);

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
  };

  const handleNext = async () => {
    if (!currentUser || !currentUser.userId) {
      setError('User not logged in. Please log in to continue.');
      setShowError(true);
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Save answer to backend
      await quizService.saveAnswer(currentUser.userId, currentQuestionId, selectedOption);

      // Update local state
      const updatedAnswers = {
        ...answers,
        [currentQuestionId]: selectedOption
      };
      setAnswers(updatedAnswers);
      localStorage.setItem('quizAnswers', JSON.stringify(updatedAnswers));

      // Navigate to next question or results
      if (currentQuestionId < quizQuestions.length) {
        navigate(`/quiz/question/${currentQuestionId + 1}`);
      } else {
        navigate('/quiz/results');
      }
    } catch (error) {
      console.error('Error saving quiz answer:', error);
      setError('Failed to save answer. Please try again.');
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (currentQuestionId > 1) {
      navigate(`/quiz/question/${currentQuestionId - 1}`);
    } else {
      navigate('/quiz');
    }
  };

  // Background style with image overlay
  const backgroundStyle = {
    backgroundImage: 'url(/images/beach-fun.jpg)',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    position: 'relative',
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 1
    }
  };

  if (!currentQuestion) {
    return (
      <Box sx={backgroundStyle}>
        <CircularProgress sx={{ color: '#F5A623', position: 'relative', zIndex: 2 }} />
      </Box>
    );
  }

  return (
    <Box sx={backgroundStyle}>
      <Card sx={{
        maxWidth: 500,
        width: '100%',
        borderRadius: 4,
        boxShadow: 3,
        position: 'relative',
        zIndex: 2,
        bgcolor: 'rgba(18, 18, 18, 0.8)',
        color: 'white',
        overflow: 'hidden'
      }}>
        <CardContent sx={{ p: 0 }}>
          {/* Header with back button and title */}
          <Box sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <IconButton onClick={handleBack} edge="start" sx={{ color: 'white' }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" component="h1" sx={{ fontWeight: 500, color: 'white' }}>
              Question {currentQuestionId}
            </Typography>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              Step 2/3
            </Typography>
          </Box>

          {/* Progress indicator */}
          <Box sx={{ width: '100%', px: 2, pt: 2 }}>
            <Box sx={{
              display: 'flex',
              width: '100%',
              height: 4,
              mb: 4
            }}>
              <Box sx={{
                width: '25%',
                height: '100%',
                bgcolor: '#F5A623',
                borderRadius: 2
              }} />
              <Box sx={{
                width: '25%',
                height: '100%',
                bgcolor: '#F5A623',
                ml: 0.5,
                borderRadius: 2
              }} />
              <Box sx={{
                width: '25%',
                height: '100%',
                bgcolor: '#F5A623',
                ml: 0.5,
                borderRadius: 2
              }} />
              <Box sx={{
                width: '25%',
                height: '100%',
                bgcolor: '#F5A623',
                ml: 0.5,
                borderRadius: 2
              }} />
            </Box>
          </Box>

          {/* Question number indicator */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
            <Box sx={{
              position: 'relative',
              width: 60,
              height: 60,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <CircularProgress
                variant="determinate"
                value={100 * (currentQuestionId / quizQuestions.length)}
                size={60}
                thickness={4}
                sx={{ color: '#F5A623', position: 'absolute' }}
              />
              <CircularProgress
                variant="determinate"
                value={100}
                size={60}
                thickness={4}
                sx={{ color: 'rgba(255, 255, 255, 0.1)', position: 'absolute' }}
              />
              <Typography variant="h6" component="div" sx={{ fontWeight: 'bold', color: 'white' }}>
                {currentQuestionId.toString().padStart(2, '0')}
              </Typography>
            </Box>
          </Box>

          {/* Main content */}
          <Box sx={{ px: 3, pb: 3, display: 'flex', flexDirection: 'column' }}>
            {/* Question */}
            <Paper
              elevation={0}
              sx={{
                p: 3,
                borderRadius: 3,
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                mb: 3
              }}
            >
              <Typography
                variant="h5"
                component="h2"
                align="center"
                sx={{
                  fontWeight: 600,
                  mb: 3,
                  color: 'white'
                }}
              >
                {currentQuestion.question}
              </Typography>

              {/* Options */}
              <FormControl component="fieldset" sx={{ width: '100%' }}>
                <RadioGroup
                  value={selectedOption}
                  onChange={handleOptionChange}
                >
                  {currentQuestion.options.map((option) => (
                    <Paper
                      key={option.id}
                      elevation={0}
                      sx={{
                        p: 2,
                        borderRadius: 2,
                        mb: 2,
                        border: '1px solid',
                        borderColor: selectedOption === option.id ? '#F5A623' : 'rgba(255, 255, 255, 0.1)',
                        bgcolor: selectedOption === option.id ? 'rgba(245, 166, 35, 0.1)' : 'rgba(255, 255, 255, 0.02)'
                      }}
                    >
                      <FormControlLabel
                        value={option.id}
                        control={
                          <Radio
                            sx={{
                              color: 'rgba(255, 255, 255, 0.7)',
                              '&.Mui-checked': {
                                color: '#F5A623',
                              },
                            }}
                          />
                        }
                        label={
                          <Typography variant="body1" sx={{ color: 'white' }}>
                            {option.id}. {option.text}
                          </Typography>
                        }
                        sx={{ width: '100%', m: 0 }}
                      />
                    </Paper>
                  ))}
                </RadioGroup>
              </FormControl>
            </Paper>

            {/* Next button */}
            <Button
              fullWidth
              variant="contained"
              onClick={handleNext}
              disabled={!selectedOption || loading}
              sx={{
                py: 1.5,
                bgcolor: '#F5A623',
                color: 'white',
                borderRadius: 8,
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: 500,
                '&:hover': {
                  bgcolor: '#d48c1f',
                },
                '&.Mui-disabled': {
                  bgcolor: 'rgba(255, 255, 255, 0.08)',
                  color: 'rgba(255, 255, 255, 0.3)'
                }
              }}
            >
              {loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={20} sx={{ color: 'white' }} />
                  Saving...
                </Box>
              ) : (
                currentQuestionId < quizQuestions.length ? 'Next' : 'See Results'
              )}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowError(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default QuizQuestion;
