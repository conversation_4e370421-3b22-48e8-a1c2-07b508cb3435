import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  IconButton,
  Paper,
  TextField,
  CircularProgress,
  Card,
  CardContent,
  Snackbar,
  Alert
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import BrushIcon from '@mui/icons-material/Brush';
import NatureIcon from '@mui/icons-material/Nature';
import userService from '../../services/user.service';

const FunFact = () => {
  const navigate = useNavigate();
  const [funFact, setFunFact] = useState('');
  const [charCount, setCharCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showError, setShowError] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const maxChars = 150;

  // Get current user from localStorage
  useEffect(() => {
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    console.log('Loaded user data from localStorage:', userData);
    console.log('User data keys:', Object.keys(userData));
    setCurrentUser(userData);
  }, []);

  // Handle input change
  const handleChange = (e) => {
    const value = e.target.value;
    if (value.length <= maxChars) {
      setFunFact(value);
      setCharCount(value.length);
    }
  };

  // Handle back button
  const handleBack = () => {
    navigate('/quiz/results');
  };



  // Handle done button
  const handleDone = async () => {
    console.log('handleDone called');
    console.log('currentUser:', currentUser);
    console.log('funFact:', funFact);

    // Check for userId or id field (handle different user data structures)
    const userId = currentUser?.userId || currentUser?.id;
    if (!currentUser || !userId) {
      console.log('User validation failed:', currentUser);
      console.log('Available user fields:', currentUser ? Object.keys(currentUser) : 'No user data');
      setError('User not logged in. Please log in to continue.');
      setShowError(true);
      return;
    }

    if (!funFact.trim()) {
      console.log('Fun fact validation failed:', funFact);
      setError('Please enter a fun fact before continuing.');
      setShowError(true);
      return;
    }

    console.log('Starting API call...');
    setLoading(true);
    setError('');

    try {
      console.log('Calling userService.saveFunFact with:', {
        userId: userId,
        funFact: funFact.trim()
      });

      // Save fun fact to backend
      const response = await userService.saveFunFact(userId, funFact.trim());
      console.log('API response:', response);

      // Update localStorage with the fun fact
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const updatedUser = {
        ...userData,
        funFact: funFact.trim()
      };
      localStorage.setItem('user', JSON.stringify(updatedUser));

      console.log('Success! Navigating to payment page...');
      // Navigate to payment page
      navigate('/payment');
    } catch (error) {
      console.error('Error saving fun fact:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      });
      setError('Failed to save fun fact. Please try again.');
      setShowError(true);
    } finally {
      setLoading(false);
    }
  };

  // Background style with image overlay
  const backgroundStyle = {
    backgroundImage: 'url(/images/beach-fun.jpg)',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    position: 'relative',
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 1
    }
  };

  return (
    <Box sx={backgroundStyle}>
      <Card sx={{
        maxWidth: 500,
        width: '100%',
        borderRadius: 4,
        boxShadow: 3,
        position: 'relative',
        zIndex: 2,
        bgcolor: 'rgba(18, 18, 18, 0.8)',
        color: 'white',
        overflow: 'hidden'
      }}>
        <CardContent sx={{ p: 0 }}>
          {/* Header with back button and title */}
          <Box sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <IconButton onClick={handleBack} edge="start" sx={{ color: 'white' }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" component="h1" sx={{ fontWeight: 500, color: 'white' }}>
              Fun Fact
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
              Step 3/3
            </Typography>
          </Box>

          {/* Question number indicator */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3, mt: 3 }}>
            <Box sx={{
              position: 'relative',
              width: 60,
              height: 60,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <CircularProgress
                variant="determinate"
                value={100}
                size={60}
                thickness={4}
                sx={{ color: '#F5A623', position: 'absolute' }}
              />
              <CircularProgress
                variant="determinate"
                value={100}
                size={60}
                thickness={4}
                sx={{ color: 'rgba(255, 255, 255, 0.1)', position: 'absolute' }}
              />
              <Typography variant="h6" component="div" sx={{ fontWeight: 'bold', color: 'white' }}>
                05
              </Typography>
            </Box>
          </Box>

          {/* Main content */}
          <Box sx={{ p: 3 }}>
            <Typography
              variant="h5"
              component="h2"
              align="center"
              sx={{
                fontWeight: 600,
                mb: 2,
                color: 'white'
              }}
            >
              Share something unique about yourself! 👆
            </Typography>

            <Paper
              elevation={0}
              sx={{
                p: 3,
                borderRadius: 2,
                mb: 3,
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)'
              }}
            >
              <Typography variant="body2" sx={{ mb: 2, color: 'rgba(255, 255, 255, 0.7)' }}>
                Things you like to do:
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <NatureIcon sx={{ mr: 1, fontSize: 18, color: '#4CAF50' }} />
                <Typography variant="body1" sx={{ color: 'white' }}>
                  Going on walks in nature
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <FitnessCenterIcon sx={{ mr: 1, fontSize: 18, color: '#F44336' }} />
                <Typography variant="body1" sx={{ color: 'white' }}>
                  Working out a few times a week
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <BrushIcon sx={{ mr: 1, fontSize: 18, color: '#9C27B0' }} />
                <Typography variant="body1" sx={{ color: 'white' }}>
                  Painting or other creative hobbies
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <MusicNoteIcon sx={{ mr: 1, fontSize: 18, color: '#2196F3' }} />
                <Typography variant="body1" sx={{ color: 'white' }}>
                  Playing an instrument or songwriting
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ mb: 2, color: 'rgba(255, 255, 255, 0.7)' }}>
                Accomplishments you're proud of:
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <EmojiEventsIcon sx={{ mr: 1, fontSize: 18, color: '#FFC107' }} />
                <Typography variant="body1" sx={{ color: 'white' }}>
                  I've traveled to 20 countries! 🌎
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <EmojiEventsIcon sx={{ mr: 1, fontSize: 18, color: '#FFC107' }} />
                <Typography variant="body1" sx={{ color: 'white' }}>
                  I can solve a Rubik's cube in 30 seconds! 🧩
                </Typography>
              </Box>
            </Paper>

            <TextField
              fullWidth
              multiline
              rows={4}
              value={funFact}
              onChange={handleChange}
              placeholder="Share your interests and accomplishments here..."
              variant="outlined"
              InputProps={{
                sx: {
                  color: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: 2,
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#F5A623',
                  },
                }
              }}
              sx={{ mb: 1 }}
            />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
              <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                Keep it short & fun!
              </Typography>
              <Typography variant="body2" sx={{ color: charCount > maxChars * 0.8 ? '#F5A623' : 'rgba(255, 255, 255, 0.5)' }}>
                {charCount}/{maxChars}
              </Typography>
            </Box>

            {/* Done button */}
            <Button
              fullWidth
              variant="contained"
              onClick={handleDone}
              disabled={!funFact.trim() || loading}
              sx={{
                py: 1.5,
                bgcolor: '#F5A623',
                color: 'white',
                borderRadius: 8,
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: 500,
                '&:hover': {
                  bgcolor: '#d48c1f',
                },
                '&.Mui-disabled': {
                  bgcolor: 'rgba(255, 255, 255, 0.08)',
                  color: 'rgba(255, 255, 255, 0.3)'
                }
              }}
            >
              {loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={20} sx={{ color: 'white' }} />
                  Saving...
                </Box>
              ) : (
                'Done'
              )}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowError(false)}
          severity="error"
          sx={{ width: '100%' }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FunFact;
