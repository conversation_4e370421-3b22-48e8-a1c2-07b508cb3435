import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import userService from '../../services/user.service';
import {
  Box,
  Button,
  Typography,
  Paper,
  CircularProgress,
  Divider,
  useTheme,
  Container,
  Card,
  CardContent,
  Avatar,
  Grid,
  Chip
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import EmojiPeopleIcon from '@mui/icons-material/EmojiPeople';
import GroupIcon from '@mui/icons-material/Group';
import StarIcon from '@mui/icons-material/Star';
import LightbulbIcon from '@mui/icons-material/Lightbulb';

const QuizResults = () => {
  const navigate = useNavigate();
  const theme = useTheme();

  const [loading, setLoading] = useState(true);
  const [personalityType, setPersonalityType] = useState(null);

  useEffect(() => {
    // Simulate loading and processing results
    const timer = setTimeout(() => {
      const savedAnswers = localStorage.getItem('quizAnswers');
      if (savedAnswers) {
        // In a real app, you would analyze the answers to determine personality type
        // For now, we'll just assign a random personality type
        const personalityTypes = [
          {
            type: 'The Connector',
            description: 'You thrive on bringing people together and creating meaningful connections. Your natural empathy and social skills make you the glue in any group.',
            traits: ['Empathetic', 'Social', 'Supportive', 'Inclusive'],
            icon: <GroupIcon sx={{ fontSize: 40, color: '#6C63FF' }} />,
            color: '#6C63FF',
            compatibleWith: ['The Adventurer', 'The Thinker'],
            strengthsTitle: 'Your Social Superpowers',
            strengths: [
              'Building bridges between different personalities',
              'Creating a welcoming atmosphere for everyone',
              'Sensing when someone needs support'
            ]
          },
          {
            type: 'The Adventurer',
            description: 'You bring spontaneity and excitement to any group. Always ready for new experiences, you help others step out of their comfort zones.',
            traits: ['Spontaneous', 'Energetic', 'Curious', 'Fun-loving'],
            icon: <EmojiPeopleIcon sx={{ fontSize: 40, color: '#F9A826' }} />,
            color: '#F9A826',
            compatibleWith: ['The Connector', 'The Organizer'],
            strengthsTitle: 'Your Adventure Toolkit',
            strengths: [
              'Bringing energy and excitement to gatherings',
              'Finding unique experiences for the group',
              'Helping others discover new interests'
            ]
          },
          {
            type: 'The Thinker',
            description: 'You bring depth and thoughtfulness to conversations. Your analytical mind and insightful perspectives enrich group discussions.',
            traits: ['Analytical', 'Thoughtful', 'Curious', 'Perceptive'],
            icon: <LightbulbIcon sx={{ fontSize: 40, color: '#43A047' }} />,
            color: '#43A047',
            compatibleWith: ['The Connector', 'The Organizer'],
            strengthsTitle: 'Your Mental Toolkit',
            strengths: [
              'Adding depth to group conversations',
              'Offering thoughtful perspectives on challenges',
              'Asking questions that spark meaningful discussions'
            ]
          },
          {
            type: 'The Organizer',
            description: 'You excel at bringing structure and clarity to group activities. Your planning skills and reliability make group experiences run smoothly.',
            traits: ['Reliable', 'Structured', 'Practical', 'Detail-oriented'],
            icon: <StarIcon sx={{ fontSize: 40, color: '#FF6584' }} />,
            color: '#FF6584',
            compatibleWith: ['The Adventurer', 'The Thinker'],
            strengthsTitle: 'Your Organization Arsenal',
            strengths: [
              'Making sure everyone has a great time',
              'Creating structure that helps the group thrive',
              'Turning ideas into actionable plans'
            ]
          }
        ];

        setPersonalityType(personalityTypes[Math.floor(Math.random() * personalityTypes.length)]);
      }
      setLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleContinue = async () => {
    try {
      // Get current user data
      const userData = JSON.parse(localStorage.getItem('user') || '{}');

      if (!userData.userId) {
        console.error('User ID not found');
        navigate('/login');
        return;
      }

      // Call backend to complete quiz and advance onboarding step
      await userService.completeQuiz(userData.userId, personalityType.type);

      // Save personality type to localStorage
      const updatedUser = {
        ...userData,
        personalityType: personalityType.type
      };
      localStorage.setItem('user', JSON.stringify(updatedUser));

      // Navigate to fun fact page
      navigate('/fun-fact');
    } catch (error) {
      console.error('Failed to complete quiz:', error);
      // Still navigate to fun fact page even if backend call fails
      navigate('/fun-fact');
    }
  };

  // Background style with image overlay
  const backgroundStyle = {
    backgroundImage: 'url(/images/beach-fun.jpg)',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    position: 'relative',
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 1
    }
  };

  if (loading) {
    return (
      <Box sx={backgroundStyle}>
        <Card sx={{
          maxWidth: 500,
          width: '100%',
          borderRadius: 4,
          boxShadow: 3,
          position: 'relative',
          zIndex: 2,
          bgcolor: 'rgba(18, 18, 18, 0.8)',
          color: 'white',
          p: 4
        }}>
          <CardContent sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center'
          }}>
            <CircularProgress size={80} sx={{ mb: 4, color: '#F5A623' }} />
            <Typography variant="h4" component="h1" sx={{ mb: 2, fontWeight: 600 }}>
              Analyzing Your Responses
            </Typography>
            <Typography variant="body1" sx={{ mb: 2, opacity: 0.8 }}>
              We're finding your perfect tribe match based on your answers...
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  if (!personalityType) {
    return (
      <Box sx={backgroundStyle}>
        <Card sx={{
          maxWidth: 500,
          width: '100%',
          borderRadius: 4,
          boxShadow: 3,
          position: 'relative',
          zIndex: 2,
          bgcolor: 'rgba(18, 18, 18, 0.8)',
          color: 'white',
          p: 4
        }}>
          <CardContent sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center'
          }}>
            <Typography variant="h5" component="h1" sx={{ mb: 3 }}>
              Something went wrong
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/quiz')}
              sx={{
                mt: 2,
                bgcolor: '#F5A623',
                '&:hover': {
                  bgcolor: '#d48c1f',
                },
                borderRadius: 8,
                px: 4,
                py: 1.5
              }}
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box sx={backgroundStyle}>
      <Card sx={{
        maxWidth: 500,
        width: '100%',
        borderRadius: 4,
        boxShadow: 3,
        position: 'relative',
        zIndex: 2,
        bgcolor: 'rgba(18, 18, 18, 0.8)',
        color: 'white',
        overflow: 'hidden'
      }}>
        {/* Colored header based on personality type */}
        <Box sx={{
          bgcolor: personalityType.color,
          py: 3,
          px: 4,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column'
        }}>
          <Avatar
            sx={{
              width: 80,
              height: 80,
              bgcolor: 'white',
              mb: 2
            }}
          >
            {personalityType.icon}
          </Avatar>

          <Typography
            variant="h4"
            component="h1"
            align="center"
            sx={{
              fontWeight: 700,
              color: 'white'
            }}
          >
            {personalityType.type}
          </Typography>
        </Box>

        <CardContent sx={{ p: 4 }}>
          {/* Description */}
          <Typography variant="body1" sx={{ mb: 4, opacity: 0.9 }}>
            {personalityType.description}
          </Typography>

          {/* Traits */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
            Your Key Traits:
          </Typography>

          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 1,
            mb: 4
          }}>
            {personalityType.traits.map((trait, index) => (
              <Chip
                key={index}
                label={trait}
                sx={{
                  bgcolor: `${personalityType.color}30`,
                  color: personalityType.color,
                  fontWeight: 500,
                  borderRadius: 4
                }}
              />
            ))}
          </Box>

          {/* Strengths */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
            {personalityType.strengthsTitle}:
          </Typography>

          <Box sx={{ mb: 4 }}>
            {personalityType.strengths.map((strength, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: personalityType.color,
                    mr: 2
                  }}
                />
                <Typography variant="body2">{strength}</Typography>
              </Box>
            ))}
          </Box>

          {/* Compatible with */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
            You Match Well With:
          </Typography>

          <Box sx={{
            display: 'flex',
            gap: 1,
            mb: 4
          }}>
            {personalityType.compatibleWith.map((type, index) => (
              <Chip
                key={index}
                label={type}
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontWeight: 500
                }}
              />
            ))}
          </Box>

          {/* What's next */}
          <Box
            sx={{
              p: 3,
              borderRadius: 3,
              mb: 4,
              bgcolor: 'rgba(245, 166, 35, 0.1)',
              border: '1px solid',
              borderColor: 'rgba(245, 166, 35, 0.3)'
            }}
          >
            <Typography
              variant="h6"
              component="h3"
              sx={{
                fontWeight: 600,
                mb: 1,
                color: '#F5A623'
              }}
            >
              What's Next?
            </Typography>

            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              We'll use your personality type to match you with compatible tribe members who share your interests and complement your social style.
            </Typography>
          </Box>

          {/* Continue button */}
          <Button
            fullWidth
            variant="contained"
            onClick={handleContinue}
            sx={{
              py: 1.5,
              bgcolor: personalityType.color,
              color: 'white',
              borderRadius: 8,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
              '&:hover': {
                bgcolor: personalityType.color,
                opacity: 0.9
              }
            }}
          >
            Continue to Profile
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
};

export default QuizResults;
