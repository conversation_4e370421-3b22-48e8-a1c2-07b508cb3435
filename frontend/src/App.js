import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import ThemeProvider from './contexts/ThemeContext';

// Components
import Navbar from './components/common/Navbar';
import Footer from './components/common/Footer';
import Login from './components/auth/Login';
import Signup from './components/auth/Signup';
import VerifyMobile from './components/auth/VerifyMobile';
import UserRegistration from './components/auth/UserRegistration';
import AvailabilitySelection from './components/auth/AvailabilitySelection';
import QuizIntro from './components/quiz/QuizIntro';
import QuizQuestion from './components/quiz/QuizQuestion';
import QuizResults from './components/quiz/QuizResults';
import FunFact from './components/quiz/FunFact';
import PaymentLanding from './components/payment/PaymentLanding';
import PaymentSuccess from './components/payment/PaymentSuccess';
import UserProfileForm from './components/profile/UserProfileForm';
import GroupStatusNew from './components/groups/GroupStatusNew';
import GroupPreview from './components/groups/GroupPreview';
import GroupFormationComplete from './components/groups/GroupFormationComplete';
import GroupSummary from './components/groups/GroupSummary';
import AdminDashboard from './components/admin/Dashboard';
import ApiTestDashboard from './components/admin/ApiTestDashboard';
import HomePage from './components/common/HomePage';
import ProtectedRoute from './components/common/ProtectedRoute';
import RedirectBasedOnProfile from './components/common/RedirectBasedOnProfile';
import GamesIceBreakers from './components/games/GamesIceBreakers';
import GoogleCallback from './components/auth/GoogleCallback';

// Theme is now managed by ThemeProvider

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Check for existing authentication on component mount
  useEffect(() => {
    // Check for authentication
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        // Check if user data exists
        if (userData && userData.userId) {
          setUser(userData);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        localStorage.removeItem('user');
      }
    }
  }, []);

  // Function to handle login
  const handleLogin = (userData) => {
    setUser(userData);
    setIsAuthenticated(true);
    localStorage.setItem('user', JSON.stringify(userData));
  };

  // Function to handle logout
  const handleLogout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('user');
    navigate('/');
  };

  // Determine if navbar should be shown
  const shouldShowNavbar = !['/verify-mobile', '/user-registration', '/availability', '/fun-fact', '/payment', '/payment-success', '/group-preview', '/group-complete', '/group-summary', '/group-status', '/redirect', '/games-ice-breakers', '/auth/google/callback'].includes(location.pathname) && !location.pathname.startsWith('/quiz');

  return (
    <ThemeProvider>
      <div className="App">
        {shouldShowNavbar && <Navbar isAuthenticated={isAuthenticated} onLogout={handleLogout} user={user} />}
        <main style={{
          minHeight: shouldShowNavbar ? 'calc(100vh - 120px)' : '100vh',
          padding: shouldShowNavbar ? '20px' : '0'
        }}>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<Login onLogin={handleLogin} />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/auth/google/callback" element={<GoogleCallback onLogin={handleLogin} />} />
            <Route path="/verify-mobile" element={<VerifyMobile />} />
            <Route path="/user-registration" element={<UserRegistration />} />
            <Route path="/availability" element={<AvailabilitySelection />} />
            <Route path="/quiz" element={<QuizIntro />} />
            <Route path="/quiz/question/:questionId" element={<QuizQuestion />} />
            <Route path="/quiz/results" element={<QuizResults />} />
            <Route path="/fun-fact" element={<FunFact />} />
            <Route path="/payment" element={<PaymentLanding user={user} />} />
            <Route path="/payment-success" element={<PaymentSuccess user={user} />} />

            {/* Protected routes */}
            <Route path="/profile" element={<UserProfileForm user={user} />} />
            <Route path="/group" element={<GroupStatusNew user={user} />} />
            <Route path="/group-preview" element={<GroupPreview user={user} />} />
            <Route path="/group-complete" element={<GroupFormationComplete user={user} />} />
            <Route path="/group-summary" element={<GroupSummary user={user} />} />
            <Route path="/admin" element={<AdminDashboard />} />
            <Route
              path="/admin/api-test"
              element={<ApiTestDashboard />}
            />
            <Route path="/games-ice-breakers" element={<GamesIceBreakers />} />

            {/* Redirect to home if route doesn't exist */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  );
}

export default App;
