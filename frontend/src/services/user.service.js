import axios from 'axios';
import authHeader from './auth-header';

const API_URL = '/api/users';

class UserService {
  // Get user profile
  getUserProfile() {
    return axios.get(`${API_URL}/profile`, { headers: authHeader() });
  }

  // Save user profile/registration data
  saveProfile(userId, profileData) {
    return axios.post(`${API_URL}/${userId}/profile`, profileData, { headers: authHeader() });
  }

  // Update user profile
  updateProfile(profileData) {
    return axios.post(`${API_URL}/profile`, profileData, { headers: authHeader() });
  }

  // Get user profile by ID
  getProfile(userId) {
    return axios.get(`${API_URL}/${userId}/profile`, { headers: authHeader() });
  }

  // Upload user photo
  uploadPhoto(userId, file) {
    const formData = new FormData();
    formData.append('file', file);

    return axios.post(`${API_URL}/${userId}/photo`, formData, {
      headers: {
        ...authHeader(),
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  // Upload all user photos
  uploadAllPhotos(userId, profilePhoto, selfiePhoto, idCardPhoto) {
    const formData = new FormData();
    formData.append('profilePhoto', profilePhoto);
    formData.append('selfiePhoto', selfiePhoto);
    formData.append('idCardPhoto', idCardPhoto);

    return axios.post(`${API_URL}/${userId}/upload-all-photos`, formData, {
      headers: {
        ...authHeader(),
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  // Save quiz results
  saveQuizResults(userId, quizResults) {
    return axios.post(`${API_URL}/${userId}/quiz-results`, quizResults, { headers: authHeader() });
  }

  // Save availability
  saveAvailability(userId, timeSlots) {
    return axios.post(`${API_URL}/${userId}/availability`, { timeSlots }, { headers: authHeader() });
  }

  // Get user's current status
  getUserStatus(userId) {
    return axios.get(`${API_URL}/${userId}/status`, { headers: authHeader() });
  }

  // Save fun fact
  saveFunFact(userId, funFact) {
    return axios.post(`${API_URL}/profile/fun-fact`, {
      userId,
      funFact
    }, { headers: authHeader() });
  }

  // Complete payment and finish onboarding
  completePayment(userId) {
    return axios.post(`${API_URL}/payment/complete`, {
      userId
    }, { headers: authHeader() });
  }
}

export default new UserService();
