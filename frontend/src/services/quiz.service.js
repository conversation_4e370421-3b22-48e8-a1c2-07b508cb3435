import axios from 'axios';
import authHeader from './auth-header';

const API_URL = '/quiz';

class QuizService {
  // Save a single quiz answer
  saveAnswer(userId, questionId, answer) {
    return axios.post(`${API_URL}/answer`, {
      userId,
      questionId,
      answer
    }, { headers: authHeader() });
  }

  // Save multiple quiz answers (batch)
  saveAnswers(userId, answers) {
    const promises = Object.entries(answers).map(([questionId, answer]) => {
      return this.saveAnswer(userId, parseInt(questionId), answer);
    });
    
    return Promise.all(promises);
  }
}

export default new QuizService();
