import axios from 'axios';

const API_URL = '/auth';

class AuthService {
  // Login user
  login(emailOrPhone, password) {
    return axios
      .post(`${API_URL}/login`, { emailOrPhone, password })
      .then(response => {
        if (response.data.userId) {
          localStorage.setItem('user', JSON.stringify(response.data));
        }
        return response.data;
      });
  }

  // Google OAuth login (placeholder for future implementation)
  googleLogin(googleToken) {
    return axios
      .post(`${API_URL}/social-login`, {
        provider: 'GOOGLE',
        accessToken: googleToken
      })
      .then(response => {
        if (response.data.userId) {
          localStorage.setItem('user', JSON.stringify(response.data));
        }
        return response.data;
      });
  }

  // Logout user
  logout() {
    localStorage.removeItem('user');
  }

  // Register user
  register(username, email, phone, password) {
    return axios.post(`${API_URL}/signup`, {
      username,
      email,
      phone,
      password
    });
  }

  // Request password reset
  requestPasswordReset(email) {
    return axios.post(`${API_URL}/forgot-password`, { email });
  }

  // Verify mobile phone
  verifyMobile(phoneNumber, otp) {
    return axios.post(`${API_URL}/verify-mobile`, { phoneNumber, otp });
  }

  // Resend OTP
  resendOTP(phoneNumber) {
    return axios.post(`${API_URL}/resend-otp`, { phoneNumber });
  }

  // Get current user
  getCurrentUser() {
    return JSON.parse(localStorage.getItem('user'));
  }

  // Check if user is logged in
  isLoggedIn() {
    const user = this.getCurrentUser();
    return !!user;
  }

  // Check if user is admin
  isAdmin() {
    const user = this.getCurrentUser();
    return user && user.role === 'ADMIN';
  }
}

export default new AuthService();
