# Google OAuth 2.0 Setup Guide

This guide explains how to set up Google OAuth 2.0 authentication for the MyTribe application.

## Prerequisites

- Google Cloud Console account
- MyTribe application running locally or deployed

## Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google Identity Services

## Step 2: Configure OAuth 2.0 Credentials

1. Navigate to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Configure the consent screen:
   - Application name: `MyTribe`
   - User support email: Your email
   - Developer contact information: Your email
4. Create OAuth 2.0 Client ID:
   - Application type: **Web application**
   - Name: `MyTribe Web Client`
   - Authorized JavaScript origins:
     - `http://localhost:3000` (for development)
     - `https://yourdomain.com` (for production)
   - Authorized redirect URIs:
     - `http://localhost:8080/auth/google/callback` (for development)
     - `https://yourdomain.com/auth/google/callback` (for production)

## Step 3: Configure Backend (Spring Boot)

1. Update `src/main/resources/application.properties`:
   ```properties
   # Replace placeholder values with your actual Google OAuth 2.0 credentials
   google.oauth2.client-id=YOUR_GOOGLE_CLIENT_ID
   google.oauth2.client-secret=YOUR_GOOGLE_CLIENT_SECRET
   google.oauth2.redirect-uri=http://localhost:8080/auth/google/callback
   google.oauth2.scope=openid,profile,email
   ```

2. Or set environment variables:
   ```bash
   export GOOGLE_CLIENT_ID=your_actual_client_id
   export GOOGLE_CLIENT_SECRET=your_actual_client_secret
   export GOOGLE_REDIRECT_URI=http://localhost:8080/auth/google/callback
   ```

## Step 4: Configure Frontend (React)

1. Create `frontend/.env` file:
   ```env
   REACT_APP_GOOGLE_CLIENT_ID=YOUR_GOOGLE_CLIENT_ID
   REACT_APP_API_URL=http://localhost:8080
   ```

2. The Google Sign-In button is already integrated in the Login component

## Step 5: Test the Integration

1. Start the backend server:
   ```bash
   ./mvnw spring-boot:run
   ```

2. Start the frontend development server:
   ```bash
   cd frontend
   npm start
   ```

3. Navigate to `http://localhost:3000/login`
4. Click the Google Sign-In button
5. Complete the OAuth flow

## Current Implementation Status

### ✅ Completed
- Google OAuth 2.0 dependencies added to Spring Boot
- Configuration properties set up with placeholder values
- Google Sign-In button added to React login page
- Google Identity Services script loaded
- Frontend Google Sign-In handler implemented
- AuthService placeholder method for Google login

### 🚧 Pending (Authentication Logic)
- Backend OAuth 2.0 token validation endpoint
- User creation/authentication flow
- JWT token generation for social login
- Error handling and security measures

## Security Considerations

1. **Never commit real credentials** to version control
2. Use environment variables for sensitive data
3. Validate all tokens on the backend
4. Implement proper CORS policies
5. Use HTTPS in production
6. Implement rate limiting for authentication endpoints

## Troubleshooting

### Common Issues

1. **"Invalid client ID"**
   - Verify the client ID matches exactly
   - Check that the domain is authorized

2. **"Redirect URI mismatch"**
   - Ensure redirect URIs are exactly configured in Google Console
   - Check for trailing slashes and protocol (http vs https)

3. **"Access blocked"**
   - Complete the OAuth consent screen configuration
   - Add test users if in development mode

### Debug Steps

1. Check browser console for JavaScript errors
2. Verify network requests in browser dev tools
3. Check backend logs for authentication errors
4. Validate environment variables are loaded correctly

## Next Steps

Once the authentication logic is implemented, the Google Sign-In will:

1. Validate the Google ID token on the backend
2. Create or authenticate the user
3. Generate a JWT token for the session
4. Redirect to the appropriate onboarding step
5. Handle errors gracefully with user feedback

## Resources

- [Google Identity Services Documentation](https://developers.google.com/identity/gsi/web)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Spring Boot OAuth 2.0 Guide](https://spring.io/guides/tutorials/spring-boot-oauth2/)
