# Google Sign-In Integration Test

## Setup Instructions

1. **Get Google Client ID**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Create OAuth 2.0 Client ID (Web application)
   - Add `http://localhost:3000` to authorized JavaScript origins
   - Copy the Client ID

2. **Configure Frontend**:
   ```bash
   cd frontend
   echo "REACT_APP_GOOGLE_CLIENT_ID=your-actual-google-client-id" > .env
   ```

3. **Configure Backend**:
   ```bash
   export GOOGLE_CLIENT_ID=your-actual-google-client-id
   ```

## Test Steps

### 1. Start Backend
```bash
./mvnw spring-boot:run
```

### 2. Start Frontend
```bash
cd frontend
npm start
```

### 3. Test Google Sign-In Flow

1. **Navigate to Login Page**: `http://localhost:3000/login`

2. **Click Google Sign-In Button**: 
   - The blue Google icon button in the social login section
   - Should trigger Google One Tap or popup

3. **Complete Google Authentication**:
   - Sign in with your Google account
   - Grant permissions

4. **Verify Backend Integration**:
   - Check browser console for "Google Sign-In successful" log
   - Check browser console for "Verified Google user" log
   - Should see success message with user info on the page

5. **Verify API Call**:
   - Open browser Network tab
   - Should see POST request to `/auth/social-login`
   - Request body should contain:
     ```json
     {
       "provider": "GOOGLE",
       "accessToken": "eyJhbGciOiJSUzI1NiIs..."
     }
     ```
   - Response should contain:
     ```json
     {
       "email": "<EMAIL>",
       "name": "User Name",
       "googleUserId": "*********",
       "profilePicture": "https://...",
       "emailVerified": true,
       "success": true,
       "message": "Google token verified successfully"
     }
     ```

## Expected Behavior

### ✅ Success Case
- Google Sign-In popup appears
- User completes authentication
- Frontend receives Google ID token
- Frontend sends token to `/auth/social-login`
- Backend verifies token with Google
- Backend returns user information
- Frontend displays success message with user details

### ❌ Error Cases
- **Invalid Token**: Backend returns error message
- **Network Error**: Frontend shows connection error
- **Google Service Unavailable**: Fallback message shown

## Manual Testing with cURL

You can also test the backend directly:

```bash
# Test with invalid token (should fail)
curl -X POST http://localhost:8080/auth/social-login \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "GOOGLE",
    "accessToken": "invalid-token"
  }'

# Test with real Google ID token (get from browser after sign-in)
curl -X POST http://localhost:8080/auth/social-login \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "GOOGLE",
    "accessToken": "PASTE_REAL_GOOGLE_ID_TOKEN_HERE"
  }'
```

## Troubleshooting

### Common Issues

1. **"Google Sign-In is not available"**
   - Check if Google script loaded: `window.google` in console
   - Verify Client ID is set correctly
   - Check browser console for script errors

2. **"Invalid Google token"**
   - Verify GOOGLE_CLIENT_ID matches in frontend and backend
   - Check token hasn't expired
   - Ensure Client ID is for web application type

3. **CORS Errors**
   - Verify `http://localhost:3000` is in Google Console authorized origins
   - Check backend CORS configuration

4. **Network Errors**
   - Ensure backend is running on port 8080
   - Check firewall/proxy settings

## Implementation Details

### Frontend Flow
1. `handleGoogleSignIn()` triggered by button click
2. Google Identity Services initialized with Client ID
3. `window.google.accounts.id.prompt()` shows sign-in
4. `handleGoogleCallback()` receives Google ID token
5. `AuthService.googleLogin()` sends token to backend
6. Success/error message displayed to user

### Backend Flow
1. `/auth/social-login` endpoint receives request
2. `GoogleTokenVerificationService.verifyGoogleToken()` validates token
3. `GoogleIdTokenVerifier` checks signature and audience
4. User information extracted from verified token
5. Response sent back to frontend

### Security Features
- Token signature verification against Google's public keys
- Audience validation to prevent token reuse
- Proper error handling and logging
- No sensitive data stored in frontend
