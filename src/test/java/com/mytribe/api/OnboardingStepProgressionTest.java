package com.mytribe.api;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import com.mytribe.api.dto.auth.SignupRequest;
import com.mytribe.entity.AuthUserEntity;
import com.mytribe.entity.UserEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.repository.UserRepository;
import com.mytribe.service.AuthService;
import com.mytribe.service.OnboardingService;
import com.mytribe.service.UserService;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class OnboardingStepProgressionTest {

    @Autowired
    private AuthService authService;

    @Autowired
    private OnboardingService onboardingService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Test
    public void testCorrectOnboardingStepProgression() {
        // 1. Create a new user - should start at REGISTERED
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPhone("1234567890");
        signupRequest.setPassword("password123");

        AuthUserEntity authUser = authService.registerWithDatabase(signupRequest);
        String userId = authUser.getId();

        // Verify initial step is REGISTERED
        OnboardingStep currentStep = onboardingService.getCurrentStep(userId);
        assertEquals("Initial step should be REGISTERED", OnboardingStep.REGISTERED, currentStep);

        // 2. Save profile data - should REMAIN at REGISTERED (not auto-advance)
        UserEntity user = userRepository.findById(userId).orElse(null);
        assertNotNull("User should exist", user);
        
        user.setName("Test User");
        user.setGender("Male");
        user.setProfession("Engineer");
        user.setZip("12345");
        userRepository.save(user);

        // Verify step is still REGISTERED after profile save
        OnboardingStep afterProfileStep = onboardingService.getCurrentStep(userId);
        assertEquals("Step should remain REGISTERED after profile save", OnboardingStep.REGISTERED, afterProfileStep);

        // 3. Complete quiz - should advance to QUIZ_COMPLETED
        onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);
        OnboardingStep afterQuizStep = onboardingService.getCurrentStep(userId);
        assertEquals("Step should advance to QUIZ_COMPLETED after quiz completion", OnboardingStep.QUIZ_COMPLETED, afterQuizStep);

        // 4. Submit fun fact - should advance to FUN_FACT_SUBMITTED
        userService.saveFunFact(userId, "I love hiking in the mountains!");
        onboardingService.updateOnboardingStep(userId, OnboardingStep.FUN_FACT_SUBMITTED);
        OnboardingStep afterFunFactStep = onboardingService.getCurrentStep(userId);
        assertEquals("Step should advance to FUN_FACT_SUBMITTED after fun fact submission", OnboardingStep.FUN_FACT_SUBMITTED, afterFunFactStep);

        // 5. Complete payment - should advance to PAYMENT_DONE
        onboardingService.updateOnboardingStep(userId, OnboardingStep.PAYMENT_DONE);
        OnboardingStep finalStep = onboardingService.getCurrentStep(userId);
        assertEquals("Step should advance to PAYMENT_DONE after payment completion", OnboardingStep.PAYMENT_DONE, finalStep);

        // 6. Verify onboarding is complete
        boolean isComplete = onboardingService.isOnboardingComplete(userId);
        assertEquals("Onboarding should be complete", true, isComplete);
    }

    @Test
    public void testProfileSaveDoesNotAdvanceOnboardingStep() {
        // Create a new user
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser2");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPhone("1234567891");
        signupRequest.setPassword("password123");

        AuthUserEntity authUser = authService.registerWithDatabase(signupRequest);
        String userId = authUser.getId();

        // Verify initial step
        assertEquals("Should start at REGISTERED", OnboardingStep.REGISTERED, onboardingService.getCurrentStep(userId));

        // Save multiple profile updates
        UserEntity user = userRepository.findById(userId).orElse(null);
        assertNotNull("User should exist", user);

        // First profile save
        user.setName("Updated Name");
        user.setGender("Female");
        userRepository.save(user);
        assertEquals("Should remain REGISTERED after first profile save", OnboardingStep.REGISTERED, onboardingService.getCurrentStep(userId));

        // Second profile save
        user.setProfession("Doctor");
        user.setEthnicity("Asian");
        userRepository.save(user);
        assertEquals("Should remain REGISTERED after second profile save", OnboardingStep.REGISTERED, onboardingService.getCurrentStep(userId));

        // Third profile save
        user.setBio("I am a doctor who loves helping people");
        user.setZip("54321");
        userRepository.save(user);
        assertEquals("Should remain REGISTERED after third profile save", OnboardingStep.REGISTERED, onboardingService.getCurrentStep(userId));

        // Verify user can only advance by explicitly completing quiz
        onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);
        assertEquals("Should advance to QUIZ_COMPLETED only after explicit quiz completion", OnboardingStep.QUIZ_COMPLETED, onboardingService.getCurrentStep(userId));
    }
}
