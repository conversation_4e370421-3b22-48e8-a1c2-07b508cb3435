package com.mytribe.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mytribe.api.dto.quiz.QuizAnswerRequest;
import com.mytribe.entity.QuizAnswerEntity;
import com.mytribe.service.QuizService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(QuizController.class)
public class QuizControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private QuizService quizService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void saveAnswer_shouldReturnSuccess() throws Exception {
        // Arrange
        QuizAnswerRequest request = new QuizAnswerRequest();
        request.setUserId("user123");
        request.setQuestionId(1);
        request.setAnswer("Strongly Agree");

        QuizAnswerEntity savedAnswer = new QuizAnswerEntity();
        savedAnswer.setUserId("user123");
        savedAnswer.setQuestionId(1);
        savedAnswer.setAnswer("Strongly Agree");

        when(quizService.saveAnswer(any(QuizAnswerRequest.class))).thenReturn(savedAnswer);

        // Act & Assert
        mockMvc.perform(post("/quiz/answer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Answer saved successfully"))
                .andExpect(jsonPath("$.userId").value("user123"))
                .andExpect(jsonPath("$.questionId").value(1))
                .andExpect(jsonPath("$.answer").value("Strongly Agree"));
    }

    @Test
    public void saveAnswer_shouldFailForMissingUserId() throws Exception {
        // Arrange
        QuizAnswerRequest request = new QuizAnswerRequest();
        request.setQuestionId(1);
        request.setAnswer("Strongly Agree");

        // Act & Assert
        mockMvc.perform(post("/quiz/answer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("User ID is required"));
    }

    @Test
    public void saveAnswer_shouldFailForInvalidQuestionId() throws Exception {
        // Arrange
        QuizAnswerRequest request = new QuizAnswerRequest();
        request.setUserId("user123");
        request.setQuestionId(0);
        request.setAnswer("Strongly Agree");

        // Act & Assert
        mockMvc.perform(post("/quiz/answer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Valid question ID is required"));
    }

    @Test
    public void saveAnswer_shouldFailForMissingAnswer() throws Exception {
        // Arrange
        QuizAnswerRequest request = new QuizAnswerRequest();
        request.setUserId("user123");
        request.setQuestionId(1);

        // Act & Assert
        mockMvc.perform(post("/quiz/answer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Answer is required"));
    }
}
