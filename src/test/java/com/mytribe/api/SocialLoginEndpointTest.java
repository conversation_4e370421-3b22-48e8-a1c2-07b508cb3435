package com.mytribe.api;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import com.mytribe.api.dto.auth.SocialLoginRequest;
import com.mytribe.api.dto.auth.SocialLoginResponse;
import com.mytribe.service.GoogleTokenVerificationService;
import com.mytribe.validator.SignupValidator;
import com.mytribe.service.AuthService;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SocialLoginEndpointTest {

    @Autowired private AuthService authService;
    @Autowired private SignupValidator signupValidator;
    @Autowired private GoogleTokenVerificationService googleTokenVerificationService;

    private AuthController authController;

    @Before
    public void setupController() {
        authController = new AuthController(authService, signupValidator, googleTokenVerificationService);
    }

    @Test
    public void socialLogin_shouldFailForNullProvider() {
        SocialLoginRequest request = new SocialLoginRequest();
        request.setProvider(null);
        request.setAccessToken("some-token");

        ResponseEntity<SocialLoginResponse> response = authController.socialLogin(request);

        assertEquals(400, response.getStatusCodeValue());
        assertFalse(response.getBody().isSuccess());
        assertEquals("Social provider is required", response.getBody().getMessage());
    }

    @Test
    public void socialLogin_shouldFailForEmptyToken() {
        SocialLoginRequest request = new SocialLoginRequest();
        request.setProvider(SocialLoginRequest.SocialProvider.GOOGLE);
        request.setAccessToken("");

        ResponseEntity<SocialLoginResponse> response = authController.socialLogin(request);

        assertEquals(400, response.getStatusCodeValue());
        assertFalse(response.getBody().isSuccess());
        assertEquals("Access token is required", response.getBody().getMessage());
    }

    @Test
    public void socialLogin_shouldFailForNullToken() {
        SocialLoginRequest request = new SocialLoginRequest();
        request.setProvider(SocialLoginRequest.SocialProvider.GOOGLE);
        request.setAccessToken(null);

        ResponseEntity<SocialLoginResponse> response = authController.socialLogin(request);

        assertEquals(400, response.getStatusCodeValue());
        assertFalse(response.getBody().isSuccess());
        assertEquals("Access token is required", response.getBody().getMessage());
    }

    @Test
    public void socialLogin_shouldFailForUnsupportedProvider() {
        SocialLoginRequest request = new SocialLoginRequest();
        request.setProvider(SocialLoginRequest.SocialProvider.FACEBOOK);
        request.setAccessToken("some-token");

        ResponseEntity<SocialLoginResponse> response = authController.socialLogin(request);

        assertEquals(400, response.getStatusCodeValue());
        assertFalse(response.getBody().isSuccess());
        assertEquals("Only Google authentication is currently supported", response.getBody().getMessage());
    }

    @Test
    public void socialLogin_shouldFailForInvalidGoogleToken() {
        SocialLoginRequest request = new SocialLoginRequest();
        request.setProvider(SocialLoginRequest.SocialProvider.GOOGLE);
        request.setAccessToken("invalid-google-token");

        ResponseEntity<SocialLoginResponse> response = authController.socialLogin(request);

        assertEquals(400, response.getStatusCodeValue());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Google token verification failed"));
    }

    @Test
    public void socialLogin_shouldAcceptValidRequestFormat() {
        SocialLoginRequest request = new SocialLoginRequest();
        request.setProvider(SocialLoginRequest.SocialProvider.GOOGLE);
        request.setAccessToken("some-token-that-will-fail-verification-but-passes-validation");

        // This should pass request validation but fail token verification
        ResponseEntity<SocialLoginResponse> response = authController.socialLogin(request);

        // Should be 400 (bad request) due to invalid token, not validation error
        assertEquals(400, response.getStatusCodeValue());
        assertFalse(response.getBody().isSuccess());
        assertTrue(response.getBody().getMessage().contains("Google token verification failed"));
    }
}
