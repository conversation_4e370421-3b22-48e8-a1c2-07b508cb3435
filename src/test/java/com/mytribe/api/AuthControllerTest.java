package com.mytribe.api;

import static org.junit.Assert.*;

import java.util.HashSet;
import java.util.Set;

import javax.transaction.Transactional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.test.context.junit4.SpringRunner;

import com.mytribe.api.dto.auth.LoginRequest;
import com.mytribe.api.dto.auth.SignupRequest;
import com.mytribe.service.AuthService;
import com.mytribe.service.GoogleTokenVerificationService;
import com.mytribe.validator.SignupValidator;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AuthControllerTest {

	@Autowired private AuthService authService;
	@Autowired private SignupValidator signupValidator;
	@Autowired private GoogleTokenVerificationService googleTokenVerificationService;

	private AuthController authController;

	@Before
	public void setupController() {
		authController = new AuthController(authService, signupValidator, googleTokenVerificationService);
	}

	@Test(expected = IllegalArgumentException.class)
	public void signup_shouldFailForBlankUsername() {
		authController.signup(createSignupRequest("", "<EMAIL>", "+1234567890", "secure123"));
	}

	@Test
	public void signup_shouldFailForInvalidUsernames() {
		String[] invalidUsernames = {"", "  ", null, "ab", "a".repeat(31), "nik@hil", ".nik", "nik..hil"};
		for (String invalid : invalidUsernames) {
			try {
				authController.signup(createSignupRequest(invalid, "<EMAIL>", "+1234567890", "secure123"));
				fail("Expected exception for invalid username: " + invalid);
			} catch (IllegalArgumentException ex) {
				assertTrue(ex.getMessage().contains("username="));
			}
		}
	}

	@Test
	public void signup_shouldFailForInvalidEmails() {
		String[] invalidEmails = {"", null, "null.com", "@gmail.com", "nikhil@"};
		for (String invalidEmail : invalidEmails) {
			try {
				authController.signup(createSignupRequest("validuser", invalidEmail, "+1234567890", "secure123"));
				fail("Expected exception for invalid email: " + invalidEmail);
			} catch (IllegalArgumentException ex) {
				assertTrue(ex.getMessage().contains("email="));
			}
		}
	}

	@Test
	public void signup_shouldFailForInvalidPasswords() {
		String[] invalidPasswords = {"", "   ", null, "123"};
		for (String badPassword : invalidPasswords) {
			try {
				authController.signup(createSignupRequest("validuser", "<EMAIL>", "+1234567890", badPassword));
				fail("Expected exception for password: " + badPassword);
			} catch (IllegalArgumentException ex) {
				assertTrue(ex.getMessage().contains("password="));
			}
		}
	}

	@Test
	public void signup_shouldFailForInvalidPhones() {
		String[] invalidPhones = {"", null, "12345", "******"};
		for (String phone : invalidPhones) {
			try {
				authController.signup(createSignupRequest("validuser", "<EMAIL>", phone, "secure123"));
				fail("Expected exception for phone: " + phone);
			} catch (IllegalArgumentException ex) {
				assertTrue(ex.getMessage().contains("phone="));
			}
		}
	}

	@Test
	public void signup_shouldSucceedWithValidInput() {
		var response = authController.signup(createSignupRequest("nikhil", "<EMAIL>", "+***********", "secure123"));
		assertNotNull(response.getUserId());
		assertEquals("Account created. Please verify your phone.", response.getMessage());
	}

	@Test
	public void signup_shouldGenerateUniqueIdsForMultipleUsers() {
		Set<String> generatedUserIds = new HashSet<>();
		for (int i = 0; i < 10; i++) {
			String username = "user" + i;
			String email = "user" + i + "@example.com";
			String phone = String.format("+1%010d", 7000000000L + i); // valid US phone
			var response = authController.signup(createSignupRequest(username, email, phone, "secure123"));
			assertFalse(generatedUserIds.contains(response.getUserId()));
			generatedUserIds.add(response.getUserId());
		}
	}


	@Test
	public void signup_shouldRejectDuplicateUsernameEmailPhone() {
		authController.signup(createSignupRequest("duplicate", "<EMAIL>", "+***********", "secure123"));

		try {
			authController.signup(createSignupRequest("unique", "<EMAIL>", "+***********", "secure123"));
			fail("Expected duplicate email error");
		} catch (IllegalArgumentException ex) {
			assertEquals("Email already registered.", ex.getMessage());
		}

		try {
			authController.signup(createSignupRequest("unique2", "<EMAIL>", "+***********", "secure123"));
			fail("Expected duplicate phone error");
		} catch (IllegalArgumentException ex) {
			assertEquals("Phone number already registered.", ex.getMessage());
		}

		try {
			authController.signup(createSignupRequest("duplicate", "<EMAIL>", "+17777777777", "secure123"));
			fail("Expected duplicate username error");
		} catch (IllegalArgumentException ex) {
			assertEquals("Username already taken.", ex.getMessage());
		}
	}

	@Test
	public void login_shouldWorkWithEmailAndPhone() {
		authController.signup(createSignupRequest("loginuser", "<EMAIL>", "+12223334444", "secure123"));

		var responseByEmail = authController.login(new LoginRequest("<EMAIL>", "secure123"));
		assertEquals("Login successful", responseByEmail.getMessage());

		var responseByPhone = authController.login(new LoginRequest("+12223334444", "secure123"));
		assertEquals("<EMAIL>", responseByPhone.getEmail());
	}

	@Test(expected = BadCredentialsException.class)
	public void login_shouldFailWithIncorrectPassword() {
		authController.signup(createSignupRequest("wrongpass", "<EMAIL>", "+12221112222", "correctpass"));
		authController.login(new LoginRequest("<EMAIL>", "wrongpass"));
	}

	private SignupRequest createSignupRequest(String username, String email, String phone, String password) {
		SignupRequest request = new SignupRequest();
		request.setUsername(username);
		request.setEmail(email);
		request.setPhone(phone);
		request.setPassword(password);
		return request;
	}
}
