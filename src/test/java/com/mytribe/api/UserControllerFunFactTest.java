package com.mytribe.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mytribe.api.dto.profile.FunFactRequest;
import com.mytribe.entity.UserEntity;
import com.mytribe.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(UserController.class)
public class UserControllerFunFactTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void saveFunFact_shouldReturnSuccess() throws Exception {
        // Arrange
        FunFactRequest request = new FunFactRequest();
        request.setUserId("user123");
        request.setFunFact("I can juggle while riding a unicycle!");

        UserEntity savedUser = new UserEntity();
        savedUser.setId("user123");
        savedUser.setFunFact("I can juggle while riding a unicycle!");

        when(userService.saveFunFact(eq("user123"), eq("I can juggle while riding a unicycle!")))
                .thenReturn(savedUser);

        // Act & Assert
        mockMvc.perform(post("/api/users/profile/fun-fact")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Fun fact saved successfully"))
                .andExpect(jsonPath("$.userId").value("user123"))
                .andExpect(jsonPath("$.funFact").value("I can juggle while riding a unicycle!"));
    }

    @Test
    public void saveFunFact_shouldFailForMissingUserId() throws Exception {
        // Arrange
        FunFactRequest request = new FunFactRequest();
        request.setFunFact("I can juggle while riding a unicycle!");

        // Act & Assert
        mockMvc.perform(post("/api/users/profile/fun-fact")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("User ID is required"));
    }

    @Test
    public void saveFunFact_shouldFailForMissingFunFact() throws Exception {
        // Arrange
        FunFactRequest request = new FunFactRequest();
        request.setUserId("user123");

        // Act & Assert
        mockMvc.perform(post("/api/users/profile/fun-fact")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Fun fact is required"));
    }

    @Test
    public void saveFunFact_shouldFailForTooLongFunFact() throws Exception {
        // Arrange
        String longFunFact = "A".repeat(151); // 151 characters
        FunFactRequest request = new FunFactRequest();
        request.setUserId("user123");
        request.setFunFact(longFunFact);

        // Act & Assert
        mockMvc.perform(post("/api/users/profile/fun-fact")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Fun fact must not exceed 150 characters"));
    }

    @Test
    public void saveFunFact_shouldHandleServiceException() throws Exception {
        // Arrange
        FunFactRequest request = new FunFactRequest();
        request.setUserId("user123");
        request.setFunFact("I can juggle while riding a unicycle!");

        when(userService.saveFunFact(any(), any()))
                .thenThrow(new IllegalArgumentException("User not found"));

        // Act & Assert
        mockMvc.perform(post("/api/users/profile/fun-fact")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("User not found"));
    }
}
