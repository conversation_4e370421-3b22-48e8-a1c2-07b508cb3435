package com.mytribe.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import com.mytribe.api.dto.auth.SignupRequest;
import com.mytribe.entity.AuthUserEntity;
import com.mytribe.entity.UserEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.repository.UserRepository;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class OnboardingStepPersistenceTest {

    @Autowired
    private AuthService authService;

    @Autowired
    private OnboardingService onboardingService;

    @Autowired
    private UserRepository userRepository;

    @Test
    public void testOnboardingStepPersistence() {
        // Create a new user
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPhone("1234567890");
        signupRequest.setPassword("password123");

        AuthUserEntity authUser = authService.registerWithDatabase(signupRequest);
        String userId = authUser.getId();

        // Verify initial step is REGISTERED
        OnboardingStep currentStep = onboardingService.getCurrentStep(userId);
        assertEquals("Initial step should be REGISTERED", OnboardingStep.REGISTERED, currentStep);

        // Update to QUIZ_COMPLETED
        UserEntity updatedUser = onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);
        assertNotNull("Updated user should not be null", updatedUser);
        assertEquals("Step should be updated to QUIZ_COMPLETED", OnboardingStep.QUIZ_COMPLETED, updatedUser.getOnboardingStep());

        // Verify persistence by reloading from database
        OnboardingStep reloadedStep = onboardingService.getCurrentStep(userId);
        assertEquals("Reloaded step should be QUIZ_COMPLETED", OnboardingStep.QUIZ_COMPLETED, reloadedStep);

        // Update to FUN_FACT_SUBMITTED
        onboardingService.updateOnboardingStep(userId, OnboardingStep.FUN_FACT_SUBMITTED);
        OnboardingStep funFactStep = onboardingService.getCurrentStep(userId);
        assertEquals("Step should be FUN_FACT_SUBMITTED", OnboardingStep.FUN_FACT_SUBMITTED, funFactStep);

        // Update to PAYMENT_DONE
        onboardingService.updateOnboardingStep(userId, OnboardingStep.PAYMENT_DONE);
        OnboardingStep finalStep = onboardingService.getCurrentStep(userId);
        assertEquals("Step should be PAYMENT_DONE", OnboardingStep.PAYMENT_DONE, finalStep);

        // Verify onboarding is complete
        boolean isComplete = onboardingService.isOnboardingComplete(userId);
        assertEquals("Onboarding should be complete", true, isComplete);

        // Double-check by directly querying the database
        UserEntity userFromDb = userRepository.findById(userId).orElse(null);
        assertNotNull("User should exist in database", userFromDb);
        assertEquals("Database should contain PAYMENT_DONE", OnboardingStep.PAYMENT_DONE, userFromDb.getOnboardingStep());
    }

    @Test
    public void testAdvanceToNextStep() {
        // Create a new user
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser2");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPhone("1234567891");
        signupRequest.setPassword("password123");

        AuthUserEntity authUser = authService.registerWithDatabase(signupRequest);
        String userId = authUser.getId();

        // Test advancing through all steps
        assertEquals("Should start at REGISTERED", OnboardingStep.REGISTERED, onboardingService.getCurrentStep(userId));

        onboardingService.advanceToNextStep(userId);
        assertEquals("Should advance to QUIZ_COMPLETED", OnboardingStep.QUIZ_COMPLETED, onboardingService.getCurrentStep(userId));

        onboardingService.advanceToNextStep(userId);
        assertEquals("Should advance to FUN_FACT_SUBMITTED", OnboardingStep.FUN_FACT_SUBMITTED, onboardingService.getCurrentStep(userId));

        onboardingService.advanceToNextStep(userId);
        assertEquals("Should advance to PAYMENT_DONE", OnboardingStep.PAYMENT_DONE, onboardingService.getCurrentStep(userId));

        // Verify no further advancement
        UserEntity finalUser = onboardingService.advanceToNextStep(userId);
        assertEquals("Should stay at PAYMENT_DONE", OnboardingStep.PAYMENT_DONE, finalUser.getOnboardingStep());
    }
}
