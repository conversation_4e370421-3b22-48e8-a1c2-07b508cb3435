package com.mytribe.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Optional;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.mytribe.entity.UserEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.repository.UserRepository;

@RunWith(MockitoJUnitRunner.class)
public class OnboardingServiceNullSafetyTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private OnboardingService onboardingService;

    private UserEntity testUser;

    @Before
    public void setUp() {
        testUser = new UserEntity();
        testUser.setId("test-user-id");
        testUser.setName("Test User");
    }

    @Test
    public void getCurrentStep_shouldReturnRegisteredWhenOnboardingStepIsNull() {
        // Given: User with null onboarding step
        testUser.setOnboardingStep(null);
        when(userRepository.findById("test-user-id")).thenReturn(Optional.of(testUser));

        // When: Getting current step
        OnboardingStep result = onboardingService.getCurrentStep("test-user-id");

        // Then: Should return REGISTERED as default
        assertNotNull("Result should not be null", result);
        assertEquals("Should return REGISTERED as default", OnboardingStep.REGISTERED, result);
    }

    @Test
    public void getCurrentStep_shouldReturnActualStepWhenNotNull() {
        // Given: User with valid onboarding step
        testUser.setOnboardingStep(OnboardingStep.QUIZ_COMPLETED);
        when(userRepository.findById("test-user-id")).thenReturn(Optional.of(testUser));

        // When: Getting current step
        OnboardingStep result = onboardingService.getCurrentStep("test-user-id");

        // Then: Should return actual step
        assertEquals("Should return actual step", OnboardingStep.QUIZ_COMPLETED, result);
    }

    @Test
    public void updateOnboardingStep_shouldHandleNullCurrentStep() {
        // Given: User with null onboarding step
        testUser.setOnboardingStep(null);
        when(userRepository.findById("test-user-id")).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(UserEntity.class))).thenReturn(testUser);

        // When: Updating onboarding step
        UserEntity result = onboardingService.updateOnboardingStep("test-user-id", OnboardingStep.QUIZ_COMPLETED);

        // Then: Should handle null gracefully and update step
        assertNotNull("Result should not be null", result);
        assertEquals("Should update to new step", OnboardingStep.QUIZ_COMPLETED, result.getOnboardingStep());
    }

    @Test
    public void updateOnboardingStep_shouldPreventNullStepAssignment() {
        // Given: User with valid onboarding step
        testUser.setOnboardingStep(OnboardingStep.REGISTERED);
        when(userRepository.findById("test-user-id")).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(UserEntity.class))).thenReturn(testUser);

        // When: Trying to update to null step (this should be prevented by null-safe setter)
        UserEntity result = onboardingService.updateOnboardingStep("test-user-id", null);

        // Then: Should default to REGISTERED instead of null
        assertNotNull("Result should not be null", result);
        assertEquals("Should default to REGISTERED when null is passed", OnboardingStep.REGISTERED, result.getOnboardingStep());
    }
}
