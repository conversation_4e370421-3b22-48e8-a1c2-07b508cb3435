package com.mytribe.service;

import com.mytribe.entity.UserEntity;
import com.mytribe.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserServiceFunFactTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private ZipCodeService zipCodeService;

    @InjectMocks
    private UserService userService;

    private UserEntity testUser;

    @BeforeEach
    void setUp() {
        testUser = new UserEntity();
        testUser.setId("user123");
        testUser.setName("Test User");
    }

    @Test
    void saveFunFact_shouldSaveSuccessfully() {
        // Arrange
        String userId = "user123";
        String funFact = "I can juggle while riding a unicycle!";
        
        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(UserEntity.class))).thenReturn(testUser);

        // Act
        UserEntity result = userService.saveFunFact(userId, funFact);

        // Assert
        assertNotNull(result);
        assertEquals(funFact, testUser.getFunFact());
        verify(userRepository).findById(userId);
        verify(userRepository).save(testUser);
    }

    @Test
    void saveFunFact_shouldThrowExceptionForNullUserId() {
        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.saveFunFact(null, "Some fun fact");
        });

        assertTrue(exception.getMessage().contains("User ID is required"));
        verify(userRepository, never()).findById(any());
        verify(userRepository, never()).save(any());
    }

    @Test
    void saveFunFact_shouldThrowExceptionForEmptyUserId() {
        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.saveFunFact("", "Some fun fact");
        });

        assertTrue(exception.getMessage().contains("User ID is required"));
        verify(userRepository, never()).findById(any());
        verify(userRepository, never()).save(any());
    }

    @Test
    void saveFunFact_shouldThrowExceptionForNullFunFact() {
        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.saveFunFact("user123", null);
        });

        assertTrue(exception.getMessage().contains("Fun fact is required"));
        verify(userRepository, never()).findById(any());
        verify(userRepository, never()).save(any());
    }

    @Test
    void saveFunFact_shouldThrowExceptionForEmptyFunFact() {
        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.saveFunFact("user123", "");
        });

        assertTrue(exception.getMessage().contains("Fun fact is required"));
        verify(userRepository, never()).findById(any());
        verify(userRepository, never()).save(any());
    }

    @Test
    void saveFunFact_shouldThrowExceptionForTooLongFunFact() {
        // Arrange
        String longFunFact = "A".repeat(151); // 151 characters

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.saveFunFact("user123", longFunFact);
        });

        assertTrue(exception.getMessage().contains("Fun fact must not exceed 150 characters"));
        verify(userRepository, never()).findById(any());
        verify(userRepository, never()).save(any());
    }

    @Test
    void saveFunFact_shouldThrowExceptionForUserNotFound() {
        // Arrange
        String userId = "nonexistent";
        String funFact = "Some fun fact";
        
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            userService.saveFunFact(userId, funFact);
        });

        assertTrue(exception.getMessage().contains("User not found"));
        verify(userRepository).findById(userId);
        verify(userRepository, never()).save(any());
    }

    @Test
    void saveFunFact_shouldTrimWhitespace() {
        // Arrange
        String userId = "user123";
        String funFactWithWhitespace = "  I can juggle while riding a unicycle!  ";
        String expectedTrimmedFunFact = "I can juggle while riding a unicycle!";
        
        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(UserEntity.class))).thenReturn(testUser);

        // Act
        UserEntity result = userService.saveFunFact(userId, funFactWithWhitespace);

        // Assert
        assertNotNull(result);
        assertEquals(expectedTrimmedFunFact, testUser.getFunFact());
        verify(userRepository).findById(userId);
        verify(userRepository).save(testUser);
    }
}
