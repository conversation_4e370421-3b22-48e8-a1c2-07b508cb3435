package com.mytribe.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import com.mytribe.api.dto.auth.SignupRequest;
import com.mytribe.entity.AuthUserEntity;
import com.mytribe.entity.UserProgressEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.repository.UserProgressRepository;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class OnboardingStepTimestampTrackingTest {

    @Autowired
    private AuthService authService;

    @Autowired
    private OnboardingService onboardingService;

    @Autowired
    private UserProgressRepository userProgressRepository;

    @Test
    public void testStepTimestampTracking() throws InterruptedException {
        // Create a new user
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPhone("1234567890");
        signupRequest.setPassword("password123");

        AuthUserEntity authUser = authService.registerWithDatabase(signupRequest);
        String userId = authUser.getId();

        // Verify initial step tracking
        List<UserProgressEntity> initialProgress = userProgressRepository.findByUserIdOrderByCompletedAt(userId);
        assertEquals("Should have one initial progress record", 1, initialProgress.size());
        assertEquals("Initial step should be REGISTERED", OnboardingStep.REGISTERED, initialProgress.get(0).getStep());
        assertNotNull("Completion timestamp should not be null", initialProgress.get(0).getCompletedAt());

        LocalDateTime registeredTimestamp = initialProgress.get(0).getCompletedAt();

        // Wait a bit to ensure different timestamps
        Thread.sleep(100);

        // Update to QUIZ_COMPLETED
        onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);

        // Verify quiz completion tracking
        List<UserProgressEntity> afterQuizProgress = userProgressRepository.findByUserIdOrderByCompletedAt(userId);
        assertEquals("Should have two progress records", 2, afterQuizProgress.size());

        // Find the quiz completion record
        Optional<UserProgressEntity> quizProgress = userProgressRepository.findByUserIdAndStep(userId, OnboardingStep.QUIZ_COMPLETED);
        assertTrue("Quiz completion record should exist", quizProgress.isPresent());
        assertNotNull("Quiz completion timestamp should not be null", quizProgress.get().getCompletedAt());
        assertTrue("Quiz completion should be after registration", 
                  quizProgress.get().getCompletedAt().isAfter(registeredTimestamp));

        LocalDateTime quizTimestamp = quizProgress.get().getCompletedAt();

        // Wait a bit to ensure different timestamps
        Thread.sleep(100);

        // Update to FUN_FACT_SUBMITTED
        onboardingService.updateOnboardingStep(userId, OnboardingStep.FUN_FACT_SUBMITTED);

        // Verify fun fact submission tracking
        List<UserProgressEntity> afterFunFactProgress = userProgressRepository.findByUserIdOrderByCompletedAt(userId);
        assertEquals("Should have three progress records", 3, afterFunFactProgress.size());

        Optional<UserProgressEntity> funFactProgress = userProgressRepository.findByUserIdAndStep(userId, OnboardingStep.FUN_FACT_SUBMITTED);
        assertTrue("Fun fact submission record should exist", funFactProgress.isPresent());
        assertTrue("Fun fact submission should be after quiz completion", 
                  funFactProgress.get().getCompletedAt().isAfter(quizTimestamp));

        // Wait a bit to ensure different timestamps
        Thread.sleep(100);

        // Update to PAYMENT_DONE
        onboardingService.updateOnboardingStep(userId, OnboardingStep.PAYMENT_DONE);

        // Verify payment completion tracking
        List<UserProgressEntity> finalProgress = userProgressRepository.findByUserIdOrderByCompletedAt(userId);
        assertEquals("Should have four progress records", 4, finalProgress.size());

        Optional<UserProgressEntity> paymentProgress = userProgressRepository.findByUserIdAndStep(userId, OnboardingStep.PAYMENT_DONE);
        assertTrue("Payment completion record should exist", paymentProgress.isPresent());
        assertTrue("Payment completion should be after fun fact submission", 
                  paymentProgress.get().getCompletedAt().isAfter(funFactProgress.get().getCompletedAt()));

        // Verify all steps are tracked with correct order
        List<UserProgressEntity> allProgress = userProgressRepository.findByUserIdOrderByCompletedAt(userId);
        assertEquals("Should have exactly 4 progress records", 4, allProgress.size());
        assertEquals("First step should be REGISTERED", OnboardingStep.REGISTERED, allProgress.get(0).getStep());
        assertEquals("Second step should be QUIZ_COMPLETED", OnboardingStep.QUIZ_COMPLETED, allProgress.get(1).getStep());
        assertEquals("Third step should be FUN_FACT_SUBMITTED", OnboardingStep.FUN_FACT_SUBMITTED, allProgress.get(2).getStep());
        assertEquals("Fourth step should be PAYMENT_DONE", OnboardingStep.PAYMENT_DONE, allProgress.get(3).getStep());
    }

    @Test
    public void testIdempotentStepTracking() throws InterruptedException {
        // Create a new user
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser2");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPhone("1234567891");
        signupRequest.setPassword("password123");

        AuthUserEntity authUser = authService.registerWithDatabase(signupRequest);
        String userId = authUser.getId();

        // Get initial timestamp
        Optional<UserProgressEntity> initialProgress = userProgressRepository.findByUserIdAndStep(userId, OnboardingStep.REGISTERED);
        assertTrue("Initial progress should exist", initialProgress.isPresent());
        LocalDateTime initialTimestamp = initialProgress.get().getCompletedAt();

        // Wait a bit to ensure different timestamps
        Thread.sleep(100);

        // Update to the same step again (should update timestamp)
        onboardingService.updateOnboardingStep(userId, OnboardingStep.REGISTERED);

        // Verify idempotence: still one record per step, but timestamp updated
        List<UserProgressEntity> allProgress = userProgressRepository.findByUserIdOrderByCompletedAt(userId);
        assertEquals("Should still have only one progress record", 1, allProgress.size());
        assertEquals("Step should still be REGISTERED", OnboardingStep.REGISTERED, allProgress.get(0).getStep());

        LocalDateTime updatedTimestamp = allProgress.get(0).getCompletedAt();
        assertTrue("Timestamp should be updated", updatedTimestamp.isAfter(initialTimestamp));

        // Wait a bit to ensure different timestamps
        Thread.sleep(100);

        // Update to QUIZ_COMPLETED twice
        onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);
        LocalDateTime firstQuizTimestamp = userProgressRepository.findByUserIdAndStep(userId, OnboardingStep.QUIZ_COMPLETED)
                .get().getCompletedAt();

        Thread.sleep(100);

        onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);
        LocalDateTime secondQuizTimestamp = userProgressRepository.findByUserIdAndStep(userId, OnboardingStep.QUIZ_COMPLETED)
                .get().getCompletedAt();

        // Verify idempotence for quiz step
        List<UserProgressEntity> finalProgress = userProgressRepository.findByUserIdOrderByCompletedAt(userId);
        assertEquals("Should have exactly two progress records", 2, finalProgress.size());
        assertTrue("Second quiz timestamp should be after first", secondQuizTimestamp.isAfter(firstQuizTimestamp));

        // Verify no duplicate records
        long registeredCount = finalProgress.stream()
                .filter(p -> p.getStep() == OnboardingStep.REGISTERED)
                .count();
        long quizCount = finalProgress.stream()
                .filter(p -> p.getStep() == OnboardingStep.QUIZ_COMPLETED)
                .count();

        assertEquals("Should have exactly one REGISTERED record", 1, registeredCount);
        assertEquals("Should have exactly one QUIZ_COMPLETED record", 1, quizCount);
    }

    @Test
    public void testCorrectTimestampUpdate() throws InterruptedException {
        // Create a new user
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser3");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPhone("1234567892");
        signupRequest.setPassword("password123");

        AuthUserEntity authUser = authService.registerWithDatabase(signupRequest);
        String userId = authUser.getId();

        // Record the initial timestamp
        LocalDateTime beforeUpdate = LocalDateTime.now();
        Thread.sleep(100);

        // Update to QUIZ_COMPLETED
        onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);

        Thread.sleep(100);
        LocalDateTime afterUpdate = LocalDateTime.now();

        // Verify timestamp is within expected range
        Optional<UserProgressEntity> quizProgress = userProgressRepository.findByUserIdAndStep(userId, OnboardingStep.QUIZ_COMPLETED);
        assertTrue("Quiz progress should exist", quizProgress.isPresent());

        LocalDateTime actualTimestamp = quizProgress.get().getCompletedAt();
        assertTrue("Timestamp should be after beforeUpdate", actualTimestamp.isAfter(beforeUpdate));
        assertTrue("Timestamp should be before afterUpdate", actualTimestamp.isBefore(afterUpdate));
    }
}
