# Server Configuration
server.port=${SERVER_PORT:8085}

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=20MB

# Database Configuration - PostgreSQL
spring.datasource.url=****************************************
spring.datasource.username=mytribe_user
spring.datasource.password=ThisIsThePassword@1411
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# JWT Configuration
jwt.secret=mytribe_secret_key_should_be_at_least_32_characters_long
jwt.expiration=86400000

# CORS Configuration
spring.mvc.cors.allowed-origins=http://localhost:3000
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.mvc.cors.allowed-headers=*
spring.mvc.cors.allow-credentials=true

# Google OAuth 2.0 Configuration
# Using provided Google OAuth 2.0 credentials
google.oauth2.client-id=${GOOGLE_CLIENT_ID:57098642797-q9vik2ts8o4g8trir0vdiem50o1j5acr.apps.googleusercontent.com}
google.oauth2.client-secret=${GOOGLE_CLIENT_SECRET:your-google-client-secret-here}
google.oauth2.redirect-uri=${GOOGLE_REDIRECT_URI:http://localhost:3000/auth/google/callback}
google.oauth2.scope=openid,profile,email