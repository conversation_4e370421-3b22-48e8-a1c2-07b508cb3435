# Server Configuration
server.port=${SERVER_PORT:8085}

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=20MB

# Database Configuration - PostgreSQL
spring.datasource.url=****************************************
spring.datasource.username=mytribe_user
spring.datasource.password=ThisIsThePassword@1411
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# JWT Configuration
jwt.secret=mytribe_secret_key_should_be_at_least_32_characters_long
jwt.expiration=86400000

# CORS Configuration
spring.mvc.cors.allowed-origins=http://localhost:3000
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.mvc.cors.allowed-headers=*
spring.mvc.cors.allow-credentials=true

# OAuth2 Configuration
# Google OAuth2
oauth2.google.client-id=${GOOGLE_CLIENT_ID:your-google-client-id}
oauth2.google.client-secret=${GOOGLE_CLIENT_SECRET:your-google-client-secret}

# Facebook OAuth2
oauth2.facebook.app-id=${FACEBOOK_APP_ID:your-facebook-app-id}
oauth2.facebook.app-secret=${FACEBOOK_APP_SECRET:your-facebook-app-secret}

# Apple OAuth2
oauth2.apple.client-id=${APPLE_CLIENT_ID:your-apple-client-id}
oauth2.apple.team-id=${APPLE_TEAM_ID:your-apple-team-id}
oauth2.apple.key-id=${APPLE_KEY_ID:your-apple-key-id}
oauth2.apple.private-key=${APPLE_PRIVATE_KEY:your-apple-private-key}