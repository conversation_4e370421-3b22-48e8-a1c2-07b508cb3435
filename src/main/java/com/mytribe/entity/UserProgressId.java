package com.mytribe.entity;

import java.io.Serializable;
import java.util.Objects;

import com.mytribe.enums.OnboardingStep;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserProgressId implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userId;
    private OnboardingStep step;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserProgressId that = (UserProgressId) o;
        return Objects.equals(userId, that.userId) && step == that.step;
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, step);
    }
}
