package com.mytribe.entity;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.CollectionTable;
import javax.persistence.Column;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserEntity {

    @Id
    private String id;

    private String name;
    private String zip;
    private double lat;
    private double lon;
    private String city;
    private String state;
    private String gender;
    private String hobby;
    private String profession;
    private String ethnicity;
    private String bio;
    private String employmentStatus;
    private String relationshipStatus;
    private boolean locationEnabled;

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "user_time_slots", joinColumns = @JoinColumn(name = "user_id"))
    @Column(name = "time_slot")
    private Set<String> availableTimeSlots = new HashSet<>();

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "user_interests", joinColumns = @JoinColumn(name = "user_id"))
    @Column(name = "interest")
    private Set<String> interests = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "group_id")
    private GroupEntity group;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private AuthUserEntity authUser;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now(); // fallback safeguard
        }
        updatedAt = LocalDateTime.now();
    }
}
