package com.mytribe.entity;

import javax.persistence.*;
import java.time.LocalDateTime;
import lombok.Data;

@Entity
@Table(name = "quiz_answers")
@Data
public class QuizAnswerEntity {
    @Id 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String userId;
    
    @Column(nullable = false)
    private Integer questionId;
    
    @Column(nullable = false)
    private String answer;
    
    private LocalDateTime submittedAt = LocalDateTime.now();
    
    @PrePersist
    protected void onCreate() {
        submittedAt = LocalDateTime.now();
    }
}
