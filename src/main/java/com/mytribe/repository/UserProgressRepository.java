package com.mytribe.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.mytribe.entity.UserProgressEntity;
import com.mytribe.entity.UserProgressId;
import com.mytribe.enums.OnboardingStep;

@Repository
public interface UserProgressRepository extends JpaRepository<UserProgressEntity, UserProgressId> {
    
    /**
     * Find all progress records for a specific user
     */
    List<UserProgressEntity> findByUserIdOrderByCompletedAt(String userId);
    
    /**
     * Find a specific progress record for a user and step
     */
    Optional<UserProgressEntity> findByUserIdAndStep(String userId, OnboardingStep step);
    
    /**
     * Check if a user has completed a specific step
     */
    boolean existsByUserIdAndStep(String userId, OnboardingStep step);
    
    /**
     * Get the latest completed step for a user
     */
    @Query("SELECT up FROM UserProgressEntity up WHERE up.userId = ?1 ORDER BY up.completedAt DESC")
    List<UserProgressEntity> findLatestProgressByUserId(String userId);
    
    /**
     * Count completed steps for a user
     */
    long countByUserId(String userId);
}
