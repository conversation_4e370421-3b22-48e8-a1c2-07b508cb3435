package com.mytribe.repository;

import com.mytribe.entity.QuizAnswerEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface QuizAnswerRepository extends JpaRepository<QuizAnswerEntity, Long> {
    List<QuizAnswerEntity> findByUserIdOrderByQuestionId(String userId);
    Optional<QuizAnswerEntity> findByUserIdAndQuestionId(String userId, Integer questionId);
    void deleteByUserIdAndQuestionId(String userId, Integer questionId);
}
