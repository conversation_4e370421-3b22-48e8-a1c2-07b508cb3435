package com.mytribe.enums;

public enum OnboardingStep {
    REGISTERED("User has completed registration"),
    QUIZ_COMPLETED("User has completed the personality quiz"),
    FUN_FACT_SUBMITTED("User has submitted their fun fact"),
    PAYMENT_DONE("User has completed payment and onboarding");

    private final String description;

    OnboardingStep(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Get the next step in the onboarding flow
     */
    public OnboardingStep getNextStep() {
        switch (this) {
            case REGISTERED:
                return QUIZ_COMPLETED;
            case QUIZ_COMPLETED:
                return FUN_FACT_SUBMITTED;
            case FUN_FACT_SUBMITTED:
                return PAYMENT_DONE;
            case PAYMENT_DONE:
                return null; // No next step, onboarding complete
            default:
                return null;
        }
    }

    /**
     * Check if onboarding is complete
     */
    public boolean isOnboardingComplete() {
        return this == PAYMENT_DONE;
    }

    /**
     * Get the frontend route for this onboarding step
     */
    public String getFrontendRoute() {
        switch (this) {
            case REGISTERED:
                return "/user-registration";
            case QUIZ_COMPLETED:
                return "/fun-fact";
            case FUN_FACT_SUBMITTED:
                return "/payment";
            case PAYMENT_DONE:
                return "/group-status";
            default:
                return "/user-registration";
        }
    }

    /**
     * Null-safe method to get onboarding step with default fallback.
     * Prevents NPE when dealing with potentially null OnboardingStep values.
     */
    public static OnboardingStep safeValueOf(OnboardingStep step) {
        return step != null ? step : REGISTERED;
    }

    /**
     * Null-safe method to check if onboarding is complete.
     * Returns false if step is null.
     */
    public static boolean isOnboardingCompleteSafe(OnboardingStep step) {
        return step != null && step.isOnboardingComplete();
    }

    /**
     * Null-safe method to get frontend route.
     * Returns default route if step is null.
     */
    public static String getFrontendRouteSafe(OnboardingStep step) {
        return step != null ? step.getFrontendRoute() : REGISTERED.getFrontendRoute();
    }
}
