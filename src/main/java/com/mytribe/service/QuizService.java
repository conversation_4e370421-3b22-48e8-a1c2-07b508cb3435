package com.mytribe.service;

import com.mytribe.api.dto.quiz.QuizAnswerRequest;
import com.mytribe.entity.QuizAnswerEntity;
import com.mytribe.repository.QuizAnswerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class QuizService {

    private final QuizAnswerRepository quizAnswerRepository;

    @Transactional
    public QuizAnswerEntity saveAnswer(QuizAnswerRequest request) {
        // Check if answer already exists for this user and question
        Optional<QuizAnswerEntity> existingAnswer = quizAnswerRepository
                .findByUserIdAndQuestionId(request.getUserId(), request.getQuestionId());
        
        QuizAnswerEntity answerEntity;
        
        if (existingAnswer.isPresent()) {
            // Update existing answer
            answerEntity = existingAnswer.get();
            answerEntity.setAnswer(request.getAnswer());
        } else {
            // Create new answer
            answerEntity = new QuizAnswerEntity();
            answerEntity.setUserId(request.getUserId());
            answerEntity.setQuestionId(request.getQuestionId());
            answerEntity.setAnswer(request.getAnswer());
        }
        
        return quizAnswerRepository.save(answerEntity);
    }
}
