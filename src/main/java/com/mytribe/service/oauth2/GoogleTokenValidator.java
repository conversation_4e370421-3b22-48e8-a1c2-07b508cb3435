package com.mytribe.service.oauth2;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Collections;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.mytribe.api.dto.auth.SocialLoginRequest;
import com.mytribe.api.dto.auth.SocialUserInfo;
import com.mytribe.config.OAuth2Config;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GoogleTokenValidator implements OAuth2TokenValidator {

    @Autowired
    private OAuth2Config oauth2Config;

    @Override
    public SocialUserInfo validateToken(SocialLoginRequest request) throws OAuth2ValidationException {
        log.info("[OAUTH2] Validating Google token for user");
        
        try {
            GoogleIdTokenVerifier verifier = new GoogleIdTokenVerifier.Builder(
                    new NetHttpTransport(), 
                    GsonFactory.getDefaultInstance())
                    .setAudience(Collections.singletonList(oauth2Config.getGoogle().getClientId()))
                    .build();

            GoogleIdToken idToken = verifier.verify(request.getAccessToken());
            
            if (idToken == null) {
                log.error("[OAUTH2] Invalid Google token provided");
                throw new OAuth2ValidationException("GOOGLE", "INVALID_TOKEN", "Invalid Google token");
            }

            GoogleIdToken.Payload payload = idToken.getPayload();
            
            // Validate audience
            if (!oauth2Config.getGoogle().getClientId().equals(payload.getAudience())) {
                log.error("[OAUTH2] Google token audience mismatch. Expected: {}, Got: {}", 
                         oauth2Config.getGoogle().getClientId(), payload.getAudience());
                throw new OAuth2ValidationException("GOOGLE", "AUDIENCE_MISMATCH", "Token audience mismatch");
            }

            String userId = payload.getSubject();
            String email = payload.getEmail();
            String name = (String) payload.get("name");
            String firstName = (String) payload.get("given_name");
            String lastName = (String) payload.get("family_name");
            String picture = (String) payload.get("picture");
            Boolean emailVerified = payload.getEmailVerified();

            log.info("[OAUTH2] Successfully validated Google token for user: {} ({})", name, email);

            return SocialUserInfo.builder()
                    .id(userId)
                    .email(email)
                    .name(name)
                    .firstName(firstName)
                    .lastName(lastName)
                    .profilePicture(picture)
                    .provider("GOOGLE")
                    .emailVerified(emailVerified != null ? emailVerified : false)
                    .build();

        } catch (GeneralSecurityException e) {
            log.error("[OAUTH2] Security error validating Google token", e);
            throw new OAuth2ValidationException("GOOGLE", "SECURITY_ERROR", "Security error during token validation", e);
        } catch (IOException e) {
            log.error("[OAUTH2] IO error validating Google token", e);
            throw new OAuth2ValidationException("GOOGLE", "IO_ERROR", "Network error during token validation", e);
        } catch (Exception e) {
            log.error("[OAUTH2] Unexpected error validating Google token", e);
            throw new OAuth2ValidationException("GOOGLE", "UNEXPECTED_ERROR", "Unexpected error during token validation", e);
        }
    }

    @Override
    public boolean supports(SocialLoginRequest.SocialProvider provider) {
        return SocialLoginRequest.SocialProvider.GOOGLE.equals(provider);
    }
}
