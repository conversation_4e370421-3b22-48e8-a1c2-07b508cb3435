package com.mytribe.service.oauth2;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mytribe.api.dto.auth.SocialLoginRequest;
import com.mytribe.api.dto.auth.SocialUserInfo;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class OAuth2Service {

    private final List<OAuth2TokenValidator> validators;

    @Autowired
    public OAuth2Service(List<OAuth2TokenValidator> validators) {
        this.validators = validators;
        log.info("[OAUTH2] Initialized OAuth2Service with {} validators", validators.size());
    }

    /**
     * Validates a social login token and returns user information
     * @param request The social login request
     * @return SocialUserInfo containing validated user information
     * @throws OAuth2ValidationException if validation fails
     */
    public SocialUserInfo validateSocialToken(SocialLoginRequest request) throws OAuth2ValidationException {
        log.info("[OAUTH2] Validating social token for provider: {}", request.getProvider());
        
        if (request.getProvider() == null) {
            log.error("[OAUTH2] Social provider is null");
            throw new OAuth2ValidationException("UNKNOWN", "MISSING_PROVIDER", "Social provider is required");
        }

        if (request.getAccessToken() == null || request.getAccessToken().trim().isEmpty()) {
            log.error("[OAUTH2] Access token is null or empty for provider: {}", request.getProvider());
            throw new OAuth2ValidationException(request.getProvider().name(), "MISSING_TOKEN", "Access token is required");
        }

        // Find the appropriate validator for the provider
        Optional<OAuth2TokenValidator> validator = validators.stream()
                .filter(v -> v.supports(request.getProvider()))
                .findFirst();

        if (!validator.isPresent()) {
            log.error("[OAUTH2] No validator found for provider: {}", request.getProvider());
            throw new OAuth2ValidationException(request.getProvider().name(), "UNSUPPORTED_PROVIDER", 
                    "Provider " + request.getProvider() + " is not supported");
        }

        try {
            SocialUserInfo userInfo = validator.get().validateToken(request);
            log.info("[OAUTH2] Successfully validated token for provider: {} user: {}", 
                    request.getProvider(), userInfo.getEmail());
            return userInfo;
        } catch (OAuth2ValidationException e) {
            log.error("[OAUTH2] Token validation failed for provider: {} error: {}", 
                     request.getProvider(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[OAUTH2] Unexpected error during token validation for provider: {}", 
                     request.getProvider(), e);
            throw new OAuth2ValidationException(request.getProvider().name(), "VALIDATION_ERROR", 
                    "Unexpected error during token validation", e);
        }
    }

    /**
     * Checks if a social provider is supported
     * @param provider The social provider
     * @return true if supported, false otherwise
     */
    public boolean isProviderSupported(SocialLoginRequest.SocialProvider provider) {
        return validators.stream().anyMatch(v -> v.supports(provider));
    }

    /**
     * Gets list of supported providers
     * @return List of supported provider names
     */
    public List<String> getSupportedProviders() {
        return List.of("GOOGLE", "FACEBOOK", "APPLE");
    }
}
