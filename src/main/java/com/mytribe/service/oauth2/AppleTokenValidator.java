package com.mytribe.service.oauth2;

import java.io.StringReader;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.mytribe.api.dto.auth.SocialLoginRequest;
import com.mytribe.api.dto.auth.SocialUserInfo;
import com.mytribe.config.OAuth2Config;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AppleTokenValidator implements OAuth2TokenValidator {

    @Autowired
    private OAuth2Config oauth2Config;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public AppleTokenValidator() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public SocialUserInfo validateToken(SocialLoginRequest request) throws OAuth2ValidationException {
        log.info("[OAUTH2] Validating Apple token for user");
        
        try {
            String identityToken = request.getIdentityToken();
            if (identityToken == null || identityToken.trim().isEmpty()) {
                log.error("[OAUTH2] Apple identity token is required but not provided");
                throw new OAuth2ValidationException("APPLE", "MISSING_IDENTITY_TOKEN", "Apple identity token is required");
            }

            // Parse the JWT token
            SignedJWT signedJWT = SignedJWT.parse(identityToken);
            
            // Get the header to extract key ID
            String keyId = signedJWT.getHeader().getKeyID();
            if (keyId == null) {
                log.error("[OAUTH2] Apple token missing key ID in header");
                throw new OAuth2ValidationException("APPLE", "MISSING_KEY_ID", "Token missing key ID");
            }

            // Fetch Apple's public keys
            RSAPublicKey publicKey = getApplePublicKey(keyId);
            
            // Verify the signature
            JWSVerifier verifier = new RSASSAVerifier(publicKey);
            if (!signedJWT.verify(verifier)) {
                log.error("[OAUTH2] Apple token signature verification failed");
                throw new OAuth2ValidationException("APPLE", "SIGNATURE_VERIFICATION_FAILED", "Token signature verification failed");
            }

            // Extract claims
            JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
            
            // Validate audience
            if (!oauth2Config.getApple().getClientId().equals(claimsSet.getAudience().get(0))) {
                log.error("[OAUTH2] Apple token audience mismatch. Expected: {}, Got: {}", 
                         oauth2Config.getApple().getClientId(), claimsSet.getAudience().get(0));
                throw new OAuth2ValidationException("APPLE", "AUDIENCE_MISMATCH", "Token audience mismatch");
            }

            // Validate issuer
            if (!"https://appleid.apple.com".equals(claimsSet.getIssuer())) {
                log.error("[OAUTH2] Apple token issuer mismatch. Expected: https://appleid.apple.com, Got: {}", 
                         claimsSet.getIssuer());
                throw new OAuth2ValidationException("APPLE", "ISSUER_MISMATCH", "Token issuer mismatch");
            }

            // Check token expiration
            if (claimsSet.getExpirationTime().getTime() < System.currentTimeMillis()) {
                log.error("[OAUTH2] Apple token has expired");
                throw new OAuth2ValidationException("APPLE", "TOKEN_EXPIRED", "Token has expired");
            }

            String userId = claimsSet.getSubject();
            String email = claimsSet.getStringClaim("email");
            Boolean emailVerified = claimsSet.getBooleanClaim("email_verified");
            
            // Apple doesn't provide name in the token after first login
            // The name is only available in the authorization response on first login
            String name = request.getName(); // Get from frontend if available
            
            log.info("[OAUTH2] Successfully validated Apple token for user: {} ({})", name != null ? name : "Unknown", email);

            return SocialUserInfo.builder()
                    .id(userId)
                    .email(email)
                    .name(name)
                    .firstName(null) // Apple doesn't provide separate first/last names in token
                    .lastName(null)
                    .profilePicture(null) // Apple doesn't provide profile pictures
                    .provider("APPLE")
                    .emailVerified(emailVerified != null ? emailVerified : false)
                    .build();

        } catch (Exception e) {
            if (e instanceof OAuth2ValidationException) {
                throw e;
            }
            log.error("[OAUTH2] Unexpected error validating Apple token", e);
            throw new OAuth2ValidationException("APPLE", "UNEXPECTED_ERROR", "Unexpected error during token validation", e);
        }
    }

    private RSAPublicKey getApplePublicKey(String keyId) throws OAuth2ValidationException {
        try {
            // Fetch Apple's public keys
            String response = webClient.get()
                    .uri("https://appleid.apple.com/auth/keys")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            JsonNode keysNode = objectMapper.readTree(response);
            JsonNode keys = keysNode.get("keys");

            // Find the key with matching key ID
            for (JsonNode key : keys) {
                if (keyId.equals(key.get("kid").asText())) {
                    String n = key.get("n").asText();
                    String e = key.get("e").asText();
                    
                    return buildRSAPublicKey(n, e);
                }
            }

            log.error("[OAUTH2] Apple public key not found for key ID: {}", keyId);
            throw new OAuth2ValidationException("APPLE", "PUBLIC_KEY_NOT_FOUND", "Public key not found for key ID: " + keyId);

        } catch (Exception e) {
            if (e instanceof OAuth2ValidationException) {
                throw e;
            }
            log.error("[OAUTH2] Error fetching Apple public keys", e);
            throw new OAuth2ValidationException("APPLE", "PUBLIC_KEY_FETCH_ERROR", "Error fetching Apple public keys", e);
        }
    }

    private RSAPublicKey buildRSAPublicKey(String n, String e) throws Exception {
        byte[] nBytes = Base64.getUrlDecoder().decode(n);
        byte[] eBytes = Base64.getUrlDecoder().decode(e);

        java.math.BigInteger modulus = new java.math.BigInteger(1, nBytes);
        java.math.BigInteger exponent = new java.math.BigInteger(1, eBytes);

        java.security.spec.RSAPublicKeySpec spec = new java.security.spec.RSAPublicKeySpec(modulus, exponent);
        KeyFactory factory = KeyFactory.getInstance("RSA");
        return (RSAPublicKey) factory.generatePublic(spec);
    }

    @Override
    public boolean supports(SocialLoginRequest.SocialProvider provider) {
        return SocialLoginRequest.SocialProvider.APPLE.equals(provider);
    }
}
