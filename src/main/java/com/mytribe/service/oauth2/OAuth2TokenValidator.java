package com.mytribe.service.oauth2;

import com.mytribe.api.dto.auth.SocialLoginRequest;
import com.mytribe.api.dto.auth.SocialUserInfo;

public interface OAuth2TokenValidator {
    
    /**
     * Validates the OAuth2 token and returns user information
     * @param request The social login request containing token and provider info
     * @return SocialUserInfo containing validated user information
     * @throws OAuth2ValidationException if token validation fails
     */
    SocialUserInfo validateToken(SocialLoginRequest request) throws OAuth2ValidationException;
    
    /**
     * Checks if this validator supports the given provider
     * @param provider The social provider
     * @return true if supported, false otherwise
     */
    boolean supports(SocialLoginRequest.SocialProvider provider);
}
