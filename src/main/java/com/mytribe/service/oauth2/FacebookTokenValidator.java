package com.mytribe.service.oauth2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.restfb.DefaultFacebookClient;
import com.restfb.FacebookClient;
import com.restfb.Parameter;
import com.restfb.Version;
import com.restfb.exception.FacebookException;
import com.restfb.types.User;
import com.mytribe.api.dto.auth.SocialLoginRequest;
import com.mytribe.api.dto.auth.SocialUserInfo;
import com.mytribe.config.OAuth2Config;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FacebookTokenValidator implements OAuth2TokenValidator {

    @Autowired
    private OAuth2Config oauth2Config;

    @Override
    public SocialUserInfo validateToken(SocialLoginRequest request) throws OAuth2ValidationException {
        log.info("[OAUTH2] Validating Facebook token for user");
        
        try {
            // Create Facebook client with the access token
            FacebookClient facebookClient = new DefaultFacebookClient(request.getAccessToken(), Version.LATEST);
            
            // Verify the token by fetching user information
            User user = facebookClient.fetchObject("me", User.class,
                    Parameter.with("fields", "id,name,email,first_name,last_name,picture.type(large)"));
            
            if (user == null || user.getId() == null) {
                log.error("[OAUTH2] Invalid Facebook token - unable to fetch user info");
                throw new OAuth2ValidationException("FACEBOOK", "INVALID_TOKEN", "Invalid Facebook token");
            }

            // Additional token validation - verify app ID
            try {
                com.restfb.types.DebugTokenInfo debugInfo = facebookClient.debugToken(request.getAccessToken());
                if (debugInfo == null || !oauth2Config.getFacebook().getAppId().equals(debugInfo.getAppId())) {
                    log.error("[OAUTH2] Facebook token app ID mismatch. Expected: {}, Got: {}", 
                             oauth2Config.getFacebook().getAppId(), debugInfo != null ? debugInfo.getAppId() : "null");
                    throw new OAuth2ValidationException("FACEBOOK", "APP_ID_MISMATCH", "Token app ID mismatch");
                }
                
                if (!debugInfo.getIsValid()) {
                    log.error("[OAUTH2] Facebook token is not valid according to debug info");
                    throw new OAuth2ValidationException("FACEBOOK", "INVALID_TOKEN", "Token is not valid");
                }
            } catch (FacebookException e) {
                log.warn("[OAUTH2] Could not debug Facebook token, proceeding with basic validation: {}", e.getMessage());
            }

            String profilePicture = null;
            if (user.getPicture() != null && user.getPicture().getData() != null) {
                profilePicture = user.getPicture().getData().getUrl();
            }

            log.info("[OAUTH2] Successfully validated Facebook token for user: {} ({})", user.getName(), user.getEmail());

            return SocialUserInfo.builder()
                    .id(user.getId())
                    .email(user.getEmail())
                    .name(user.getName())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .profilePicture(profilePicture)
                    .provider("FACEBOOK")
                    .emailVerified(true) // Facebook emails are generally verified
                    .build();

        } catch (FacebookException e) {
            log.error("[OAUTH2] Facebook API error validating token", e);
            throw new OAuth2ValidationException("FACEBOOK", "API_ERROR", "Facebook API error: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("[OAUTH2] Unexpected error validating Facebook token", e);
            throw new OAuth2ValidationException("FACEBOOK", "UNEXPECTED_ERROR", "Unexpected error during token validation", e);
        }
    }

    @Override
    public boolean supports(SocialLoginRequest.SocialProvider provider) {
        return SocialLoginRequest.SocialProvider.FACEBOOK.equals(provider);
    }
}
