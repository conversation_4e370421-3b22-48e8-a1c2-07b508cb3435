package com.mytribe.service.oauth2;

public class OAuth2ValidationException extends Exception {
    
    private final String provider;
    private final String errorCode;
    
    public OAuth2ValidationException(String provider, String message) {
        super(message);
        this.provider = provider;
        this.errorCode = "VALIDATION_FAILED";
    }
    
    public OAuth2ValidationException(String provider, String message, Throwable cause) {
        super(message, cause);
        this.provider = provider;
        this.errorCode = "VALIDATION_FAILED";
    }
    
    public OAuth2ValidationException(String provider, String errorCode, String message) {
        super(message);
        this.provider = provider;
        this.errorCode = errorCode;
    }
    
    public OAuth2ValidationException(String provider, String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.provider = provider;
        this.errorCode = errorCode;
    }
    
    public String getProvider() {
        return provider;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
