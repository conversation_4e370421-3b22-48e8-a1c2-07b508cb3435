package com.mytribe.service;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Collections;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.mytribe.api.dto.auth.SocialLoginResponse;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GoogleTokenVerificationService {

    @Value("${google.oauth2.client-id}")
    private String googleClientId;

    /**
     * Verifies Google ID token and extracts user information
     * @param idToken The Google ID token to verify
     * @return SocialLoginResponse with user information
     * @throws GoogleTokenVerificationException if verification fails
     */
    public SocialLoginResponse verifyGoogleToken(String idToken) throws GoogleTokenVerificationException {
        log.info("[GOOGLE_AUTH] Starting Google token verification");
        
        try {
            // Create GoogleIdTokenVerifier
            GoogleIdTokenVerifier verifier = new GoogleIdTokenVerifier.Builder(
                    new NetHttpTransport(), 
                    GsonFactory.getDefaultInstance())
                    .setAudience(Collections.singletonList(googleClientId))
                    .build();

            // Verify the token
            GoogleIdToken googleIdToken = verifier.verify(idToken);
            
            if (googleIdToken == null) {
                log.error("[GOOGLE_AUTH] Invalid Google ID token provided");
                throw new GoogleTokenVerificationException("Invalid Google ID token");
            }

            // Extract payload
            GoogleIdToken.Payload payload = googleIdToken.getPayload();
            
            // Validate audience
            if (!googleClientId.equals(payload.getAudience())) {
                log.error("[GOOGLE_AUTH] Google token audience mismatch. Expected: {}, Got: {}", 
                         googleClientId, payload.getAudience());
                throw new GoogleTokenVerificationException("Token audience mismatch");
            }

            // Extract user information
            String userId = payload.getSubject();
            String email = payload.getEmail();
            String name = (String) payload.get("name");
            String picture = (String) payload.get("picture");
            Boolean emailVerified = payload.getEmailVerified();

            log.info("[GOOGLE_AUTH] Successfully verified Google token for user: {} ({})", name, email);

            // Build response
            return SocialLoginResponse.builder()
                    .email(email)
                    .name(name)
                    .googleUserId(userId)
                    .profilePicture(picture)
                    .emailVerified(emailVerified != null ? emailVerified : false)
                    .success(true)
                    .message("Google token verified successfully")
                    .build();

        } catch (GeneralSecurityException e) {
            log.error("[GOOGLE_AUTH] Security error during Google token verification", e);
            throw new GoogleTokenVerificationException("Security error during token verification: " + e.getMessage(), e);
        } catch (IOException e) {
            log.error("[GOOGLE_AUTH] IO error during Google token verification", e);
            throw new GoogleTokenVerificationException("Network error during token verification: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("[GOOGLE_AUTH] Unexpected error during Google token verification", e);
            throw new GoogleTokenVerificationException("Unexpected error during token verification: " + e.getMessage(), e);
        }
    }

    /**
     * Custom exception for Google token verification errors
     */
    public static class GoogleTokenVerificationException extends Exception {
        public GoogleTokenVerificationException(String message) {
            super(message);
        }
        
        public GoogleTokenVerificationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
