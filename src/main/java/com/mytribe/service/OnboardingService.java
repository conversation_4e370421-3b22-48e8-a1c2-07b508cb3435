package com.mytribe.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mytribe.entity.UserEntity;
import com.mytribe.entity.UserProgressEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.repository.UserProgressRepository;
import com.mytribe.repository.UserRepository;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
public class OnboardingService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProgressRepository userProgressRepository;

    /**
     * Update user's onboarding step
     */
    @Transactional
    public UserEntity updateOnboardingStep(String userId, OnboardingStep step) {
        // Handle null step parameter with safe default
        if (step == null) {
            log.warn("[ONBOARDING] Null step provided for user {}, defaulting to REGISTERED", userId);
            step = OnboardingStep.REGISTERED;
        }

        log.info("[ONBOARDING] Updating onboarding step for user {} to {}", userId, step);

        Optional<UserEntity> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            log.error("[ONBOARDING] User not found: {}", userId);
            throw new IllegalArgumentException("User not found");
        }

        UserEntity user = userOpt.get();
        OnboardingStep currentStep = getOnboardingStepSafely(user);

        log.info("[ONBOARDING] Current step for user {}: {}, updating to: {}", userId, currentStep, step);

        // Validate step progression (optional - can be removed if you want to allow skipping)
        if (!isValidStepProgression(currentStep, step)) {
            log.warn("[ONBOARDING] Invalid step progression for user {}: {} -> {}", userId, currentStep, step);
            // For now, we'll allow it but log a warning
        }

        // Set the onboarding step directly (Lombok will generate the setter)
        user.setOnboardingStep(step);
        log.info("[ONBOARDING] Set onboarding step on entity for user {} to: {}", userId, step);

        // Verify the step was set correctly before saving
        OnboardingStep verifyStep = user.getOnboardingStep();
        log.info("[ONBOARDING] Verified step on entity for user {}: {}", userId, verifyStep);

        UserEntity savedUser = userRepository.save(user);

        // Verify the step was persisted correctly after saving
        OnboardingStep persistedStep = savedUser.getOnboardingStep();
        log.info("[ONBOARDING] Persisted step for user {}: {}", userId, persistedStep);

        // Track step completion timestamp in user_progress table
        trackStepCompletion(userId, step);

        // Double-check by reloading from database
        UserEntity reloadedUser = userRepository.findById(userId).orElse(null);
        if (reloadedUser != null) {
            OnboardingStep reloadedStep = reloadedUser.getOnboardingStep();
            log.info("[ONBOARDING] Reloaded step from database for user {}: {}", userId, reloadedStep);

            if (!step.equals(reloadedStep)) {
                log.error("[ONBOARDING] PERSISTENCE FAILURE! Expected: {}, but database contains: {} for user {}",
                         step, reloadedStep, userId);
            } else {
                log.info("[ONBOARDING] SUCCESS! Step {} correctly persisted for user {}", step, userId);
            }
        }

        return savedUser;
    }

    /**
     * Get user's current onboarding step
     */
    public OnboardingStep getCurrentStep(String userId) {
        Optional<UserEntity> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            log.error("[ONBOARDING] User not found: {}", userId);
            throw new IllegalArgumentException("User not found");
        }

        OnboardingStep step = getOnboardingStepSafely(userOpt.get());
        log.info("[ONBOARDING] Current onboarding step for user {}: {}", userId, step);
        return step;
    }

    /**
     * Get the next step for a user
     */
    public OnboardingStep getNextStep(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        return currentStep.getNextStep();
    }

    /**
     * Check if user has completed onboarding
     */
    public boolean isOnboardingComplete(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        return currentStep.isOnboardingComplete();
    }

    /**
     * Get the frontend route for user's current step
     */
    public String getCurrentStepRoute(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        return currentStep.getFrontendRoute();
    }

    /**
     * Advance user to the next onboarding step
     */
    @Transactional
    public UserEntity advanceToNextStep(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        OnboardingStep nextStep = currentStep.getNextStep();

        if (nextStep == null) {
            log.info("User {} is already at the final onboarding step: {}", userId, currentStep);
            return userRepository.findById(userId).get();
        }

        return updateOnboardingStep(userId, nextStep);
    }

    /**
     * Validate if step progression is valid (optional validation)
     */
    private boolean isValidStepProgression(OnboardingStep currentStep, OnboardingStep newStep) {
        // Handle null parameters
        if (newStep == null) {
            log.warn("[ONBOARDING] New step is null, considering invalid progression");
            return false;
        }

        if (currentStep == null) {
            return newStep == OnboardingStep.REGISTERED;
        }

        // Allow staying at the same step or moving forward
        if (newStep == currentStep) {
            return true;
        }

        // Check if it's the next logical step
        OnboardingStep expectedNext = currentStep.getNextStep();
        return newStep == expectedNext;
    }

    /**
     * Reset user's onboarding to the beginning (for testing/admin purposes)
     */
    @Transactional
    public UserEntity resetOnboarding(String userId) {
        log.info("Resetting onboarding for user {}", userId);
        return updateOnboardingStep(userId, OnboardingStep.REGISTERED);
    }

    /**
     * Production-grade null-safe method to get onboarding step.
     * Provides multiple layers of protection against null values.
     */
    private OnboardingStep getOnboardingStepSafely(UserEntity user) {
        if (user == null) {
            log.warn("[ONBOARDING] UserEntity is null, returning default onboarding step: REGISTERED");
            return OnboardingStep.REGISTERED;
        }

        // Use the safe getter method
        OnboardingStep step = user.getOnboardingStepSafe();
        log.debug("[ONBOARDING] Retrieved onboarding step for user {}: {}", user.getId(), step);
        return step;
    }

    /**
     * Track step completion timestamp in user_progress table.
     * Implements upsert logic: insert if absent, update completed_at if present.
     * Ensures idempotence: one row per (user, step).
     */
    private void trackStepCompletion(String userId, OnboardingStep step) {
        try {
            log.info("[ONBOARDING] Tracking step completion for user {} step {}", userId, step);

            // Check if record already exists
            Optional<UserProgressEntity> existingProgress = userProgressRepository.findByUserIdAndStep(userId, step);

            if (existingProgress.isPresent()) {
                // Update existing record with new timestamp
                UserProgressEntity progress = existingProgress.get();
                LocalDateTime oldTimestamp = progress.getCompletedAt();
                progress.setCompletedAt(LocalDateTime.now());
                userProgressRepository.save(progress);

                log.info("[ONBOARDING] Updated step completion timestamp for user {} step {} from {} to {}",
                        userId, step, oldTimestamp, progress.getCompletedAt());
            } else {
                // Insert new record
                UserProgressEntity newProgress = new UserProgressEntity();
                newProgress.setUserId(userId);
                newProgress.setStep(step);
                newProgress.setCompletedAt(LocalDateTime.now());
                userProgressRepository.save(newProgress);

                log.info("[ONBOARDING] Created new step completion record for user {} step {} at {}",
                        userId, step, newProgress.getCompletedAt());
            }

            log.info("[ONBOARDING] Successfully tracked step completion for user {} step {}", userId, step);

        } catch (Exception e) {
            log.error("[ONBOARDING] Failed to track step completion for user {} step {}: {}",
                     userId, step, e.getMessage(), e);
            // Don't throw exception to avoid breaking the main onboarding flow
            // The step tracking is supplementary functionality
        }
    }
}
