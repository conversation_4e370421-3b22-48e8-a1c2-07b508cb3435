package com.mytribe.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mytribe.entity.UserEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.repository.UserRepository;

import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Service
@Slf4j
public class OnboardingService {

    @Autowired
    private UserRepository userRepository;

    /**
     * Update user's onboarding step
     */
    @Transactional
    public UserEntity updateOnboardingStep(String userId, OnboardingStep step) {
        log.info("Updating onboarding step for user {} to {}", userId, step);

        Optional<UserEntity> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            log.error("User not found: {}", userId);
            throw new IllegalArgumentException("User not found");
        }

        UserEntity user = userOpt.get();
        OnboardingStep currentStep = user.getOnboardingStep();

        // Validate step progression (optional - can be removed if you want to allow skipping)
        if (!isValidStepProgression(currentStep, step)) {
            log.warn("Invalid step progression for user {}: {} -> {}", userId, currentStep, step);
            // For now, we'll allow it but log a warning
        }

        user.setOnboardingStep(step);
        UserEntity savedUser = userRepository.save(user);

        log.info("Successfully updated onboarding step for user {} to {}", userId, step);
        return savedUser;
    }

    /**
     * Get user's current onboarding step
     */
    public OnboardingStep getCurrentStep(String userId) {
        Optional<UserEntity> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            log.error("User not found: {}", userId);
            throw new IllegalArgumentException("User not found");
        }

        OnboardingStep step = userOpt.get().getOnboardingStep();
        log.debug("Current onboarding step for user {}: {}", userId, step);
        return step;
    }

    /**
     * Get the next step for a user
     */
    public OnboardingStep getNextStep(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        return currentStep.getNextStep();
    }

    /**
     * Check if user has completed onboarding
     */
    public boolean isOnboardingComplete(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        return currentStep.isOnboardingComplete();
    }

    /**
     * Get the frontend route for user's current step
     */
    public String getCurrentStepRoute(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        return currentStep.getFrontendRoute();
    }

    /**
     * Advance user to the next onboarding step
     */
    @Transactional
    public UserEntity advanceToNextStep(String userId) {
        OnboardingStep currentStep = getCurrentStep(userId);
        OnboardingStep nextStep = currentStep.getNextStep();

        if (nextStep == null) {
            log.info("User {} is already at the final onboarding step: {}", userId, currentStep);
            return userRepository.findById(userId).get();
        }

        return updateOnboardingStep(userId, nextStep);
    }

    /**
     * Validate if step progression is valid (optional validation)
     */
    private boolean isValidStepProgression(OnboardingStep currentStep, OnboardingStep newStep) {
        if (currentStep == null) {
            return newStep == OnboardingStep.REGISTERED;
        }

        // Allow staying at the same step or moving forward
        if (newStep == currentStep) {
            return true;
        }

        // Check if it's the next logical step
        OnboardingStep expectedNext = currentStep.getNextStep();
        return newStep == expectedNext;
    }

    /**
     * Reset user's onboarding to the beginning (for testing/admin purposes)
     */
    @Transactional
    public UserEntity resetOnboarding(String userId) {
        log.info("Resetting onboarding for user {}", userId);
        return updateOnboardingStep(userId, OnboardingStep.REGISTERED);
    }
}
