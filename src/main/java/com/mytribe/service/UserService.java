package com.mytribe.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mytribe.api.dto.User.UserRegistrationRequest;
import com.mytribe.entity.UserEntity;
import com.mytribe.model.User;
import com.mytribe.model.ZipLocation;
import com.mytribe.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Service
@Slf4j
public class UserService {

	@Autowired
	private ZipCodeService zipCodeService;

	@Autowired
	private UserRepository userRepository;

	public User createUserFromRegistration(UserRegistrationRequest req) {
		ZipLocation loc = zipCodeService.getLocationByZip(req.getZipCode());

		if (loc == null) return null;

		User user = new User(req.getZipCode(), loc.lat, loc.lon, loc.city, loc.stateId, req.getGender(), null);
		user.name = req.getName();
		user.profession = req.getProfession();
		user.ethnicity = req.getEthnicity();
		user.bio = req.getBio();
		user.employmentStatus = req.getEmploymentStatus();
		user.relationshipStatus = req.getRelationshipStatus();
		user.locationEnabled = req.isLocationEnabled();
		return user;
	}

	@Transactional
	public UserEntity saveFunFact(String userId, String funFact) {
		try {
			log.info("Saving fun fact for user: {}", userId);

			// Validate input
			if (userId == null || userId.trim().isEmpty()) {
				throw new IllegalArgumentException("User ID is required");
			}

			if (funFact == null || funFact.trim().isEmpty()) {
				throw new IllegalArgumentException("Fun fact is required");
			}

			if (funFact.length() > 150) {
				throw new IllegalArgumentException("Fun fact must not exceed 150 characters");
			}

			// Find user by ID
			Optional<UserEntity> userOpt = userRepository.findById(userId);
			if (!userOpt.isPresent()) {
				log.error("User not found: {}", userId);
				throw new IllegalArgumentException("User not found");
			}

			UserEntity user = userOpt.get();
			user.setFunFact(funFact.trim());

			// Save to database
			UserEntity savedUser = userRepository.save(user);
			log.info("Successfully saved fun fact for user {}: {}", userId, funFact);

			return savedUser;

		} catch (Exception e) {
			log.error("Error saving fun fact for user {}: {}", userId, e.getMessage(), e);
			throw new RuntimeException("Failed to save fun fact: " + e.getMessage());
		}
	}
}
