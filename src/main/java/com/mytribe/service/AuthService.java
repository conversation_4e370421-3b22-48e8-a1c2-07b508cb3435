package com.mytribe.service;

import java.util.Collections;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mytribe.api.dto.auth.LoginRequest;
import com.mytribe.api.dto.auth.LoginResponse;
import com.mytribe.api.dto.auth.SignupRequest;
import com.mytribe.entity.AuthUserEntity;
import com.mytribe.entity.UserEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.repository.AuthUserRepository;
import com.mytribe.repository.UserRepository;
import com.mytribe.security.JwtTokenUtil;

@Service
public class AuthService {

	@Autowired private PasswordEncoder passwordEncoder;
	@Autowired private AuthUserRepository authUserRepository;
	@Autowired private UserRepository userRepository;
	@Autowired private AuthenticationManager authenticationManager;
	@Autowired private JwtTokenUtil jwtTokenUtil;

	@Transactional
	public AuthUserEntity registerWithDatabase(SignupRequest request) {
		if (authUserRepository.existsByUsername(request.getUsername()))
			throw new IllegalArgumentException("Username already taken.");
		if (authUserRepository.existsByEmail(request.getEmail()))
			throw new IllegalArgumentException("Email already registered.");
		if (authUserRepository.existsByPhone(request.getPhone()))
			throw new IllegalArgumentException("Phone number already registered.");

		String userId = UUID.randomUUID().toString();

		UserEntity user = new UserEntity();
		user.setId(userId);
		user.setName(request.getUsername());
		user.setOnboardingStep(OnboardingStep.REGISTERED);
		userRepository.save(user);

		AuthUserEntity authUser = new AuthUserEntity();
		authUser.setId(userId);
		authUser.setUsername(request.getUsername());
		authUser.setEmail(request.getEmail());
		authUser.setPhone(request.getPhone());
		authUser.setPassword(passwordEncoder.encode(request.getPassword()));
		authUser.setRoles(Collections.singleton("USER"));
		authUser.setUser(user);

		return authUserRepository.save(authUser);
	}

	@Transactional
	public LoginResponse loginWithDatabase(LoginRequest request) {
		AuthUserEntity user = authUserRepository.findByEmail(request.getEmailOrPhone())
				.orElseGet(() -> authUserRepository.findByPhone(request.getEmailOrPhone())
						.orElseGet(() -> authUserRepository.findByUsername(request.getEmailOrPhone())
								.orElseThrow(() -> new IllegalArgumentException("User not found"))));

		Authentication authentication = authenticationManager.authenticate(
				new UsernamePasswordAuthenticationToken(user.getUsername(), request.getPassword()));
		SecurityContextHolder.getContext().setAuthentication(authentication);

		String role = user.getRoles().contains("ADMIN") ? "ADMIN" : "USER";
		String token = jwtTokenUtil.generateToken(user.getUsername(), user.getId(), role);

		// Get user's onboarding information with null safety
		UserEntity userEntity = user.getUser();
		OnboardingStep onboardingStep = getOnboardingStepSafely(userEntity);
		boolean profileCompleted = onboardingStep.isOnboardingComplete();
		String nextRoute = onboardingStep.getFrontendRoute();

		LoginResponse response = new LoginResponse();
		response.setUserId(user.getId());
		response.setToken(token);
		response.setUsername(user.getUsername());
		response.setEmail(user.getEmail());
		response.setRole(role);
		response.setMessage("Login successful");
		response.setOnboardingStep(onboardingStep);
		response.setNextRoute(nextRoute);
		response.setProfileCompleted(profileCompleted);
		return response;
	}

	public String getAuthStatus() {
		return "AuthUserRepository: " + (authUserRepository != null)
				+ "\nAuthenticationManager: " + (authenticationManager != null)
				+ "\nJwtTokenUtil: " + (jwtTokenUtil != null)
				+ "\nUserRepository: " + (userRepository != null);
	}

	/**
	 * Production-grade null-safe method to get onboarding step.
	 * Handles multiple layers of null safety:
	 * 1. Null userEntity
	 * 2. Null onboarding step from database
	 * 3. Provides safe default (REGISTERED)
	 */
	private OnboardingStep getOnboardingStepSafely(UserEntity userEntity) {
		if (userEntity == null) {
			return OnboardingStep.REGISTERED;
		}

		// UserEntity.getOnboardingStep() is already null-safe, but add extra protection
		OnboardingStep step = userEntity.getOnboardingStep();
		return step != null ? step : OnboardingStep.REGISTERED;
	}
}
