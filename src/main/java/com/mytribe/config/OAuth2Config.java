package com.mytribe.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Configuration
@ConfigurationProperties(prefix = "oauth2")
@Data
public class OAuth2Config {

    private Google google = new Google();
    private Facebook facebook = new Facebook();
    private Apple apple = new Apple();

    @Data
    public static class Google {
        private String clientId;
        private String clientSecret;
    }

    @Data
    public static class Facebook {
        private String appId;
        private String appSecret;
    }

    @Data
    public static class Apple {
        private String clientId;
        private String teamId;
        private String keyId;
        private String privateKey;
    }
}
