package com.mytribe.api;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import com.mytribe.api.dto.User.UserRegistrationRequest;
import com.mytribe.api.dto.profile.FunFactRequest;
import com.mytribe.api.dto.profile.FunFactResponse;
import com.mytribe.entity.UserEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.model.User;
import com.mytribe.repository.UserRepository;
import com.mytribe.service.OnboardingService;
import com.mytribe.service.UserPhotoUploadService;
import com.mytribe.service.UserService;

import javax.validation.Valid;

import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;

@RestController
@RequestMapping("/api/users")
public class UserController {

	@Autowired
	private UserService userService;

	@Autowired
	private UserPhotoUploadService photoUploadService;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private OnboardingService onboardingService;

	@PostMapping("/register")
	public User register(@RequestBody UserRegistrationRequest request) {
		User user = userService.createUserFromRegistration(request);
		if (user == null) {
			throw new IllegalArgumentException("Invalid ZIP code or request data.");
		}
		return user;
	}

	@PostMapping("/{userId}/photo")
	public Map<String, String> uploadPhoto(
			@PathVariable String userId,
			@RequestParam("file") MultipartFile file
	) {
		Map<String, String> response = new HashMap<>();

		if (file.isEmpty()) {
			response.put("error", "File is empty");
			return response;
		}

		try {
			// Clean original filename (remove spaces/special characters)
			String original = file.getOriginalFilename();
			String cleanName = original.replaceAll("[^a-zA-Z0-9\\.\\-]", "_");
			String folder = new File("uploads/user-photos").getAbsolutePath() + "/";

			String filename = userId + "_" + cleanName;

			// Ensure upload directory exists
			File dir = new File(folder);
			if (!dir.exists()) dir.mkdirs();

			// Save file
			File dest = new File(folder + filename);
			file.transferTo(dest);

			response.put("message", "Photo uploaded successfully");
			response.put("fileName", filename);
			response.put("relativePath", folder + filename);
			return response;

		} catch (IOException e) {
			e.printStackTrace();
			throw new RuntimeException("Failed to save photo.", e);
		}
	}

	@PostMapping("/{userId}/upload-all-photos")
	public Map<String, String> uploadAllPhotos(
			@PathVariable String userId,
			@RequestParam("profilePhoto") MultipartFile profilePhoto,
			@RequestParam("selfiePhoto") MultipartFile selfiePhoto,
			@RequestParam("idCardPhoto") MultipartFile idCardPhoto
	) {
		photoUploadService.uploadAll(userId, profilePhoto, selfiePhoto, idCardPhoto);
		return Map.of("message", "All 3 photos uploaded successfully");
	}

	@PostMapping("/{userId}/availability")
	@Transactional
	public ResponseEntity<Map<String, Object>> saveAvailability(
			@PathVariable String userId,
			@RequestBody Map<String, Object> request) {

		Map<String, Object> response = new HashMap<>();

		// --- basic validation ---------------------------------------------------
		if (userId == null || userId.trim().isEmpty()) {
			response.put("success", false);
			response.put("message", "User ID is required");
			return ResponseEntity.badRequest().body(response);
		}

		UserEntity user = userRepository.findById(userId)
				.orElseThrow(() -> new IllegalArgumentException("User not found"));

		Object timeSlotsObj = request.get("timeSlots");
		if (!(timeSlotsObj instanceof List)) {
			response.put("success", false);
			response.put("message", "timeSlots must be a non-empty list");
			return ResponseEntity.badRequest().body(response);
		}

		List<?> slotsList = (List<?>) timeSlotsObj;
		if (slotsList.isEmpty()) {
			response.put("success", false);
			response.put("message", "timeSlots cannot be empty");
			return ResponseEntity.badRequest().body(response);
		}

		// --- persist ------------------------------------------------------------
		Set<String> newSlots = slotsList.stream()
				.map(Object::toString)
				.collect(Collectors.toSet());

		user.setAvailableTimeSlots(newSlots);   // @ElementCollection handled by JPA
		userRepository.save(user);              // flushes availability row changes

		// --- response -----------------------------------------------------------
		response.put("success", true);
		response.put("message", "Availability saved successfully");
		response.put("userId", userId);
		response.put("savedCount", newSlots.size());
		response.put("timeSlots", newSlots);
		return ResponseEntity.ok(response);
	}


	@PostMapping("/{userId}/profile")
	@Transactional
	public ResponseEntity<Map<String, Object>> saveProfile(
			@PathVariable String userId,
			@RequestBody Map<String, Object> profileData) {

		Map<String, Object> response = new HashMap<>();

		if (userId == null || userId.trim().isEmpty()) {
			response.put("success", false);
			response.put("message", "User ID is required");
			return ResponseEntity.badRequest().body(response);
		}

		UserEntity user = userRepository.findById(userId)
				.orElseThrow(() -> new IllegalArgumentException("User not found"));

		// simple field mapping – expand as needed
		if (profileData.containsKey("name"))
			user.setName((String) profileData.get("name"));

		if (profileData.containsKey("gender"))
			user.setGender((String) profileData.get("gender"));

		if (profileData.containsKey("profession"))
			user.setProfession((String) profileData.get("profession"));

		if (profileData.containsKey("ethnicity"))
			user.setEthnicity((String) profileData.get("ethnicity"));

		if (profileData.containsKey("shortBio"))
			user.setBio((String) profileData.get("shortBio"));

		if (profileData.containsKey("employmentStatus"))
			user.setEmploymentStatus((String) profileData.get("employmentStatus"));

		if (profileData.containsKey("relationshipStatus"))
			user.setRelationshipStatus((String) profileData.get("relationshipStatus"));

		if (profileData.containsKey("zipCode"))
			user.setZip((String) profileData.get("zipCode"));

		if (profileData.containsKey("enableLocation"))
			user.setLocationEnabled(Boolean.parseBoolean(String.valueOf(profileData.get("enableLocation"))));

		// persist
		userRepository.save(user);

		// Update onboarding step to indicate profile completion
		// Note: We advance to QUIZ_COMPLETED since profile completion means they should go to quiz
		onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);

		response.put("success", true);
		response.put("message", "Profile saved successfully");
		response.put("userId", userId);
		return ResponseEntity.ok(response);
	}

	@PostMapping("/profile/fun-fact")
	public ResponseEntity<FunFactResponse> saveFunFact(@Valid @RequestBody FunFactRequest request) {
		try {
			// Validate request
			if (request.getUserId() == null || request.getUserId().trim().isEmpty()) {
				return ResponseEntity.badRequest()
						.body(new FunFactResponse(false, "User ID is required"));
			}

			if (request.getFunFact() == null || request.getFunFact().trim().isEmpty()) {
				return ResponseEntity.badRequest()
						.body(new FunFactResponse(false, "Fun fact is required"));
			}

			if (request.getFunFact().length() > 150) {
				return ResponseEntity.badRequest()
						.body(new FunFactResponse(false, "Fun fact must not exceed 150 characters"));
			}

			// Save fun fact using service
			UserEntity savedUser = userService.saveFunFact(request.getUserId(), request.getFunFact());

			// Update onboarding step to indicate fun fact submission
			onboardingService.updateOnboardingStep(request.getUserId(), OnboardingStep.FUN_FACT_SUBMITTED);

			FunFactResponse response = new FunFactResponse(
					true,
					"Fun fact received successfully",
					savedUser.getId(),
					request.getFunFact() // Use the request fun fact since it's not saved to DB yet
			);

			return ResponseEntity.ok(response);

		} catch (IllegalArgumentException e) {
			return ResponseEntity.badRequest()
					.body(new FunFactResponse(false, e.getMessage()));
		} catch (Exception e) {
			return ResponseEntity.internalServerError()
					.body(new FunFactResponse(false, "Failed to save fun fact: " + e.getMessage()));
		}
	}

	@PostMapping("/payment/complete")
	public ResponseEntity<Map<String, Object>> completePayment(@RequestBody Map<String, String> request) {
		Map<String, Object> response = new HashMap<>();

		try {
			String userId = request.get("userId");

			if (userId == null || userId.trim().isEmpty()) {
				response.put("success", false);
				response.put("message", "User ID is required");
				return ResponseEntity.badRequest().body(response);
			}

			// Update onboarding step to indicate payment completion
			onboardingService.updateOnboardingStep(userId, OnboardingStep.PAYMENT_DONE);

			response.put("success", true);
			response.put("message", "Payment completed successfully. Onboarding complete!");
			response.put("userId", userId);
			response.put("onboardingComplete", true);
			return ResponseEntity.ok(response);

		} catch (Exception e) {
			response.put("success", false);
			response.put("message", "Failed to complete payment: " + e.getMessage());
			return ResponseEntity.badRequest().body(response);
		}
	}

}
