package com.mytribe.api;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.mytribe.api.dto.auth.LoginRequest;
import com.mytribe.api.dto.auth.LoginResponse;
import com.mytribe.api.dto.auth.SignupRequest;
import com.mytribe.api.dto.auth.SignupResponse;
import com.mytribe.api.dto.auth.SocialLoginRequest;
import com.mytribe.api.dto.auth.SocialLoginResponse;
import com.mytribe.service.AuthService;
import com.mytribe.service.GoogleTokenVerificationService;
import com.mytribe.validator.SignupValidator;

@RestController
@RequestMapping("/auth")
public class AuthController {

	private final AuthService authService;
	private final SignupValidator signupValidator;
	private final GoogleTokenVerificationService googleTokenVerificationService;

	public AuthController(AuthService authService, SignupValidator signupValidator,
						 GoogleTokenVerificationService googleTokenVerificationService) {
		this.authService = authService;
		this.signupValidator = signupValidator;
		this.googleTokenVerificationService = googleTokenVerificationService;
	}

	@PostMapping("/signup")
	public SignupResponse signup(@RequestBody SignupRequest request) {
		signupValidator.validate(request);
		var entity = authService.registerWithDatabase(request);
		return new SignupResponse(entity.getId(), "Account created. Please verify your phone.");
	}

	@PostMapping("/login")
	public LoginResponse login(@RequestBody LoginRequest request) {
		if (request.getEmailOrPhone() == null || request.getPassword() == null)
			throw new IllegalArgumentException("Email/Phone and password are required.");
		return authService.loginWithDatabase(request);
	}

	@PostMapping("/social-login")
	public ResponseEntity<SocialLoginResponse> socialLogin(@RequestBody SocialLoginRequest request) {
		try {
			// Validate request
			if (request.getProvider() == null) {
				return ResponseEntity.badRequest().body(
					SocialLoginResponse.builder()
						.success(false)
						.message("Social provider is required")
						.build());
			}

			if (request.getAccessToken() == null || request.getAccessToken().trim().isEmpty()) {
				return ResponseEntity.badRequest().body(
					SocialLoginResponse.builder()
						.success(false)
						.message("Access token is required")
						.build());
			}

			// Currently only Google is supported
			if (request.getProvider() != SocialLoginRequest.SocialProvider.GOOGLE) {
				return ResponseEntity.badRequest().body(
					SocialLoginResponse.builder()
						.success(false)
						.message("Only Google authentication is currently supported")
						.build());
			}

			// Verify Google token and extract user information
			SocialLoginResponse response = googleTokenVerificationService.verifyGoogleToken(request.getAccessToken());
			return ResponseEntity.ok(response);

		} catch (GoogleTokenVerificationService.GoogleTokenVerificationException e) {
			return ResponseEntity.badRequest().body(
				SocialLoginResponse.builder()
					.success(false)
					.message("Google token verification failed: " + e.getMessage())
					.build());
		} catch (Exception e) {
			return ResponseEntity.internalServerError().body(
				SocialLoginResponse.builder()
					.success(false)
					.message("Internal server error during social login")
					.build());
		}
	}

	@PostMapping("/verify-mobile")
	public ResponseEntity<Map<String, Object>> verifyMobile(@RequestBody Map<String, String> request) {
		String phone = request.get("phoneNumber");
		String otp = request.get("otp");
		Map<String, Object> res = new HashMap<>();
		if (phone == null || otp == null) {
			res.put("success", false);
			res.put("message", "Phone number and OTP are required");
			return ResponseEntity.badRequest().body(res);
		}
		if (otp.matches("\\d{6}")) {
			res.put("success", true);
			res.put("message", "Phone number verified successfully");
			return ResponseEntity.ok(res);
		}
		res.put("success", false);
		res.put("message", "Invalid OTP code");
		return ResponseEntity.badRequest().body(res);
	}

	@PostMapping("/resend-otp")
	public ResponseEntity<Map<String, Object>> resendOTP(@RequestBody Map<String, String> request) {
		String phone = request.get("phoneNumber");
		Map<String, Object> res = new HashMap<>();
		if (phone == null) {
			res.put("success", false);
			res.put("message", "Phone number is required");
			return ResponseEntity.badRequest().body(res);
		}
		System.out.println("Sending OTP to: " + phone + " (simulated)");
		res.put("success", true);
		res.put("message", "OTP sent successfully");
		return ResponseEntity.ok(res);
	}

	@PostMapping("/forgot-password")
	public ResponseEntity<Map<String, Object>> forgotPassword(@RequestBody Map<String, String> request) {
		String email = request.get("email");
		Map<String, Object> res = new HashMap<>();
		if (email == null || email.trim().isEmpty()) {
			res.put("success", false);
			res.put("message", "Email is required");
			return ResponseEntity.badRequest().body(res);
		}
		if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
			res.put("success", false);
			res.put("message", "Invalid email format");
			return ResponseEntity.badRequest().body(res);
		}
		System.out.println("Sending password reset email to: " + email + " (simulated)");
		res.put("success", true);
		res.put("message", "Password reset email sent successfully");
		return ResponseEntity.ok(res);
	}

	@GetMapping("/status")
	public ResponseEntity<Map<String, Object>> getAuthStatus() {
		Map<String, Object> res = new HashMap<>();
		res.put("authStatus", authService.getAuthStatus());
		res.put("timestamp", java.time.Instant.now().toString());
		return ResponseEntity.ok(res);
	}
}
