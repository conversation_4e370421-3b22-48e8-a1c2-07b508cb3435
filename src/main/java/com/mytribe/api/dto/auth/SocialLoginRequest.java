package com.mytribe.api.dto.auth;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SocialLoginRequest {
    
    @NotNull(message = "Provider is required")
    private SocialProvider provider;
    
    @NotBlank(message = "Access token is required")
    private String accessToken;
    
    // For Apple, we also need the identity token
    private String identityToken;
    
    // Optional: User info from frontend (for validation)
    private String email;
    private String name;
    private String profilePicture;
    
    public enum SocialProvider {
        GOOGLE, FACEBOOK, APPLE
    }
}
