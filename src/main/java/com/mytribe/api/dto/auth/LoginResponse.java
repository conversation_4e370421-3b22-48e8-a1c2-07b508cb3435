package com.mytribe.api.dto.auth;

import com.mytribe.enums.OnboardingStep;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class LoginResponse {
	private String userId;
	private String username;
	private String email;
	private String token;
	private String role;
	private String message;
	private OnboardingStep onboardingStep;
	private String nextRoute;
	private boolean profileCompleted;

	public LoginResponse(String userId, String message) {
		this.userId = userId;
		this.message = message;
	}
}
