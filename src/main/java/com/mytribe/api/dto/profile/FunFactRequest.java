package com.mytribe.api.dto.profile;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class FunFactRequest {
    @NotBlank(message = "User ID is required")
    private String userId;
    
    @NotBlank(message = "Fun fact is required")
    @Size(max = 150, message = "Fun fact must not exceed 150 characters")
    private String funFact;
}
