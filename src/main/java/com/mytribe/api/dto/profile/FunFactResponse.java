package com.mytribe.api.dto.profile;

import lombok.Data;

@Data
public class FunFactResponse {
    private boolean success;
    private String message;
    private String userId;
    private String funFact;
    
    public FunFactResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    public FunFactResponse(boolean success, String message, String userId, String funFact) {
        this.success = success;
        this.message = message;
        this.userId = userId;
        this.funFact = funFact;
    }
}
