package com.mytribe.api.dto.quiz;

import lombok.Data;

@Data
public class QuizAnswerResponse {
    private boolean success;
    private String message;
    private String userId;
    private Integer questionId;
    private String answer;
    
    public QuizAnswerResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    public QuizAnswerResponse(boolean success, String message, String userId, Integer questionId, String answer) {
        this.success = success;
        this.message = message;
        this.userId = userId;
        this.questionId = questionId;
        this.answer = answer;
    }
}
