package com.mytribe.api;

import com.mytribe.api.dto.quiz.QuizAnswerRequest;
import com.mytribe.api.dto.quiz.QuizAnswerResponse;
import com.mytribe.entity.QuizAnswerEntity;
import com.mytribe.enums.OnboardingStep;
import com.mytribe.service.OnboardingService;
import com.mytribe.service.QuizService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/quiz")
@RequiredArgsConstructor
public class QuizController {

    private final QuizService quizService;
    private final OnboardingService onboardingService;

    @PostMapping("/answer")
    public ResponseEntity<QuizAnswerResponse> saveAnswer(@Valid @RequestBody QuizAnswerRequest request) {
        try {
            // Validate request
            if (request.getUserId() == null || request.getUserId().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new QuizAnswerResponse(false, "User ID is required"));
            }

            if (request.getQuestionId() == null || request.getQuestionId() <= 0) {
                return ResponseEntity.badRequest()
                        .body(new QuizAnswerResponse(false, "Valid question ID is required"));
            }

            if (request.getAnswer() == null || request.getAnswer().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new QuizAnswerResponse(false, "Answer is required"));
            }

            // Save the answer
            QuizAnswerEntity savedAnswer = quizService.saveAnswer(request);

            QuizAnswerResponse response = new QuizAnswerResponse(
                    true,
                    "Answer saved successfully",
                    savedAnswer.getUserId(),
                    savedAnswer.getQuestionId(),
                    savedAnswer.getAnswer()
            );

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new QuizAnswerResponse(false, "Failed to save answer: " + e.getMessage()));
        }
    }

    @PostMapping("/complete")
    public ResponseEntity<Map<String, Object>> completeQuiz(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();

        try {
            String userId = request.get("userId");
            String personalityType = request.get("personalityType");

            if (userId == null || userId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "User ID is required");
                return ResponseEntity.badRequest().body(response);
            }

            if (personalityType == null || personalityType.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "Personality type is required");
                return ResponseEntity.badRequest().body(response);
            }

            // Update onboarding step to indicate quiz completion
            // This is the correct place to advance from REGISTERED to QUIZ_COMPLETED
            onboardingService.updateOnboardingStep(userId, OnboardingStep.QUIZ_COMPLETED);

            response.put("success", true);
            response.put("message", "Quiz completed successfully");
            response.put("userId", userId);
            response.put("personalityType", personalityType);
            response.put("nextStep", "fun-fact");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to complete quiz: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
