package com.mytribe.api;

import com.mytribe.api.dto.quiz.QuizAnswerRequest;
import com.mytribe.api.dto.quiz.QuizAnswerResponse;
import com.mytribe.entity.QuizAnswerEntity;
import com.mytribe.service.QuizService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/quiz")
@RequiredArgsConstructor
public class QuizController {

    private final QuizService quizService;

    @PostMapping("/answer")
    public ResponseEntity<QuizAnswerResponse> saveAnswer(@Valid @RequestBody QuizAnswerRequest request) {
        try {
            // Validate request
            if (request.getUserId() == null || request.getUserId().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new QuizAnswerResponse(false, "User ID is required"));
            }
            
            if (request.getQuestionId() == null || request.getQuestionId() <= 0) {
                return ResponseEntity.badRequest()
                        .body(new QuizAnswerResponse(false, "Valid question ID is required"));
            }
            
            if (request.getAnswer() == null || request.getAnswer().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new QuizAnswerResponse(false, "Answer is required"));
            }
            
            // Save the answer
            QuizAnswerEntity savedAnswer = quizService.saveAnswer(request);
            
            QuizAnswerResponse response = new QuizAnswerResponse(
                    true, 
                    "Answer saved successfully",
                    savedAnswer.getUserId(),
                    savedAnswer.getQuestionId(),
                    savedAnswer.getAnswer()
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new QuizAnswerResponse(false, "Failed to save answer: " + e.getMessage()));
        }
    }
}
