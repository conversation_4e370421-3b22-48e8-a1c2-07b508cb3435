package com.mytribe.api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
@RequiredArgsConstructor
public class OpenAiClient {
	private final RestTemplate rest;          // or WebClient
	@Value("${openai.secret}") String apiKey;

	public String chat(String prompt){
		// minimal POST to https://api.openai.com/v1/chat/completions
		// model=gpt-3.5-turbo, messages=[{role:"user",content:prompt}]
		// parse “choices[0].message.content”
		// (error-handling omitted for brevity)
		return "Gi";
	}
}
